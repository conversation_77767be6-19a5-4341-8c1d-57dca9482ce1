<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>健康检测报告 - 健康评估系统</title>
  <link rel="stylesheet" href="../assets/css/main.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    .report-page {
      max-width: 800px;
      margin: 0 auto;
    }

    .report-header {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      padding: 20px;
      border-radius: var(--border-radius);
      margin-bottom: 30px;
      text-align: center;
      box-shadow: var(--shadow);
      position: relative;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .report-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
      pointer-events: none;
    }

    .report-header-content {
      position: relative;
      z-index: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 15px;
      width: 100%;
      max-width: 600px;
      margin: 0 auto;
    }

    .report-header img.logo {
      max-width: 180px;
      width: 100%;
      height: auto;
      display: block;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      transition: transform 0.3s ease;
      margin: 0 auto;
    }

    .report-header img.logo:hover {
      transform: scale(1.05);
    }

    .report-header h1 {
      color: white;
      margin: 0;
      font-size: 1.8rem;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
      letter-spacing: 0.5px;
      line-height: 1.2;
      text-align: center;
      width: 100%;
    }

    .report-date {
      font-size: 0.95rem;
      font-weight: 500;
      background-color: rgba(255, 255, 255, 0.2);
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 10px 16px;
      border-radius: 25px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
      margin: 0 auto;
      text-align: center;
    }

    .report-date:hover {
      background-color: rgba(255, 255, 255, 0.25);
      transform: translateY(-1px);
    }

    .report-date i {
      font-size: 0.9rem;
    }

    /* 响应式设计优化 */
    @media (max-width: 768px) {
      .report-header {
        padding: 15px;
        margin-bottom: 20px;
      }

      .report-header-content {
        gap: 12px;
      }

      .report-header img.logo {
        max-width: 140px;
      }

      .report-header h1 {
        font-size: 1.4rem;
        text-align: center;
        line-height: 1.3;
      }

      .report-date {
        font-size: 0.85rem;
        padding: 8px 14px;
        flex-wrap: wrap;
        justify-content: center;
        text-align: center;
      }

      .user-info-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }

      .container {
        padding: 15px;
      }
    }

    @media (max-width: 480px) {
      .report-header {
        padding: 12px;
        margin-bottom: 15px;
      }

      .report-header-content {
        gap: 10px;
      }

      .report-header img.logo {
        max-width: 120px;
      }

      .report-header h1 {
        font-size: 1.2rem;
        line-height: 1.4;
      }

      .report-date {
        font-size: 0.8rem;
        padding: 6px 12px;
        max-width: 100%;
        word-break: break-word;
      }

      .container {
        padding: 10px;
      }

      .user-info-card {
        padding: 15px;
        margin-bottom: 20px;
      }

      .user-info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
      }

      .user-info-label {
        min-width: auto;
        font-size: 0.9rem;
      }

      .user-info-value {
        font-size: 0.95rem;
      }
    }

    /* 确保在超小屏幕上的可读性 */
    @media (max-width: 360px) {
      .report-header img.logo {
        max-width: 100px;
      }

      .report-header h1 {
        font-size: 1.1rem;
      }

      .report-date {
        font-size: 0.75rem;
        padding: 5px 10px;
      }

      .container {
        padding: 8px;
      }

      .report-header {
        padding: 10px;
      }

      .user-info-card {
        padding: 12px;
      }
    }

    /* 横屏模式优化 */
    @media (max-width: 768px) and (orientation: landscape) {
      .report-header {
        padding: 10px 15px;
      }

      .report-header-content {
        flex-direction: row;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
        justify-content: center;
      }

      .report-header img.logo {
        max-width: 80px;
        margin: 0;
      }

      .report-header h1 {
        font-size: 1.3rem;
        margin: 0;
        flex: 1;
        text-align: left;
        min-width: 200px;
      }

      .report-date {
        font-size: 0.8rem;
        margin: 0;
      }
    }

    /* 确保文字不会溢出 */
    .report-header h1,
    .report-date {
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    /* 桌面端优化 */
    @media (min-width: 769px) {
      .report-header {
        padding: 30px 20px;
        margin-bottom: 40px;
      }

      .report-header-content {
        gap: 20px;
        max-width: 700px;
      }

      .report-header img.logo {
        max-width: 200px;
      }

      .report-header h1 {
        font-size: 2rem;
      }

      .report-date {
        font-size: 1rem;
        padding: 12px 20px;
      }
    }

    /* 超大屏幕优化 */
    @media (min-width: 1200px) {
      .report-header {
        padding: 40px 30px;
      }

      .report-header-content {
        max-width: 800px;
        gap: 25px;
      }

      .report-header img.logo {
        max-width: 220px;
      }

      .report-header h1 {
        font-size: 2.2rem;
      }

      .report-date {
        font-size: 1.1rem;
        padding: 14px 24px;
      }
    }

    /* 优化触摸设备的交互 */
    @media (hover: none) and (pointer: coarse) {
      .report-header img.logo:hover {
        transform: none;
      }

      .report-date:hover {
        background-color: rgba(255, 255, 255, 0.2);
        transform: none;
      }
    }

    .user-info-card {
      background-color: var(--card-background);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      padding: 20px;
      margin-bottom: 30px;
    }

    .user-info-title {
      font-size: 1.2rem;
      color: var(--primary-color);
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      align-items: center;
    }

    .user-info-title i {
      margin-right: 10px;
    }

    .user-info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }

    .user-info-item {
      display: flex;
      align-items: center;
    }

    .user-info-label {
      font-weight: 500;
      margin-right: 10px;
      color: var(--light-text);
      min-width: 80px;
    }

    .user-info-value {
      font-weight: 500;
    }

    .section-title {
      background-color: #f0f7ff;
      padding: 15px;
      border-radius: var(--border-radius);
      margin: 25px 0 15px;
      font-weight: 600;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }

    .section-title i {
      margin-right: 10px;
    }

    .duplicate-item {
      position: relative;
    }

    .duplicate-item::after {
      content: "重复";
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      background-color: var(--accent-color);
      color: #333;
      padding: 3px 8px;
      border-radius: 4px;
      font-size: 0.75rem;
      font-weight: 500;
    }

    .symptom-info {
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: var(--border-radius);
      margin-top: 10px;
      font-size: 0.95rem;
      border-left: 3px solid var(--primary-color);
    }

    .symptom-title {
      font-weight: 600;
      margin-bottom: 8px;
      color: var(--primary-color);
    }

    .report-actions {
      display: flex;
      justify-content: space-between;
      margin-top: 30px;
    }

    .report-action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    /* 加载和错误消息样式 */
    .loading-message {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      color: var(--light-text);
      font-size: 1rem;
      background-color: var(--card-background);
      border-radius: var(--border-radius);
      margin: 30px 0;
      box-shadow: var(--shadow);
      text-align: center;
    }

    .loading-spinner {
      margin-bottom: 20px;
    }

    .error-message {
      background-color: #fff8f8;
      border-left: 4px solid var(--danger-color);
      border-radius: var(--border-radius);
      padding: 25px;
      margin: 30px 0;
      color: var(--text-color);
      box-shadow: var(--shadow);
    }

    .error-message h2 {
      color: var(--danger-color);
      margin-top: 0;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      font-size: 1.3rem;
    }

    .error-message h2 i {
      margin-right: 10px;
    }

    .error-message ul {
      margin-left: 25px;
      margin-bottom: 20px;
      line-height: 1.6;
    }

    .error-message li {
      margin-bottom: 8px;
    }

    .error-message p:last-child {
      margin-bottom: 0;
      font-weight: 500;
      color: var(--primary-color);
    }

    /* 报告数据表格 */
    .report-table {
      width: 100%;
      border-collapse: collapse;
      margin: 15px 0;
      box-shadow: var(--shadow);
      border-radius: var(--border-radius);
      overflow: hidden;
      font-size: 0.9rem;
    }

    .report-table th {
      background-color: #f5f5f5;
      padding: 8px 10px;
      text-align: left;
      font-weight: 600;
      color: var(--primary-color);
      border-bottom: 1px solid var(--border-color);
      font-size: 0.85rem;
    }

    .report-table td {
      padding: 8px 10px;
      border-bottom: 1px solid var(--border-color);
      font-size: 0.85rem;
    }

    .report-table tr:last-child td {
      border-bottom: none;
    }

    .report-table tr:hover {
      background-color: #f9f9f9;
    }

    .status-normal {
      color: var(--secondary-color);
      font-weight: 500;
    }

    .status-warning {
      color: var(--accent-color);
      font-weight: 500;
    }

    .status-danger {
      color: var(--danger-color);
      font-weight: 500;
    }

    /* 打印样式 */
    @media print {
      .navbar, .footer, .report-actions {
        display: none;
      }

      .container {
        max-width: 100%;
        padding: 0;
      }

      .report-header {
        box-shadow: none;
      }

      .user-info-card, .report-container > div {
        box-shadow: none;
        border: 1px solid #ddd;
      }

      .report-page {
        max-width: 100%;
      }
    }

    .collapsible-section {
      margin-bottom: 20px;
    }

    .collapsible-header {
      background-color: #f0f7ff;
      padding: 15px;
      border-radius: var(--border-radius);
      font-weight: 600;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      justify-content: space-between; /* 让箭头在右边 */
      cursor: pointer;
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
      transition: background-color 0.3s ease;
    }

    .collapsible-header:hover {
      background-color: #e6f2ff;
    }

    .collapsible-header i.arrow {
      margin-left: 10px;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* Smoother rotation for arrow */
    }

    .collapsible-header.active i.arrow {
      transform: rotate(90deg);
    }

    .collapsible-content {
      padding: 0 15px;
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1),
                  padding 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      background-color: var(--card-background);
      border: 1px solid #e0e0e0;
      border-top: none;
      border-radius: 0 0 var(--border-radius) var(--border-radius);
    }

    .collapsible-content.active {
      padding: 15px;
      max-height: 2000px; 
      transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1),
                  padding 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Styles for nested collapsible headers */
    .nested-collapsible-header {
      background-color: #f8f9fa; /* Lighter background for nested items */
      color: var(--text-color); /* Standard text color for nested items */
      font-weight: 500; /* Slightly less bold than main headers */
      padding: 12px 15px; /* Adjusted padding */
      border-top: 1px solid #e9ecef; /* Add a top border for separation */
    }

    .nested-collapsible-header:hover {
      background-color: #e9ecef; /* Slightly darker hover for nested items */
    }

    .nested-collapsible-header i.arrow {
      color: var(--primary-color); /* Keep arrow color consistent or adjust as needed */
    }

    /* Ensure the main collapsible header styles are not overridden where not intended */
    .collapsible-header:not(.nested-collapsible-header) {
        /* Styles specific to main headers if needed, or rely on defaults */
    }

    /* 移动端表格优化 */
    @media (max-width: 768px) {
      .report-table {
        margin: 10px 0;
        font-size: 0.8rem;
        border-radius: 6px;
      }

      .report-table th {
        padding: 6px 8px;
        font-size: 0.75rem;
        line-height: 1.2;
      }

      .report-table td {
        padding: 6px 8px;
        font-size: 0.75rem;
        line-height: 1.3;
      }

      /* 状态列样式优化 */
      .status-normal, .status-warning, .status-danger {
        font-weight: 600;
        font-size: 0.75rem;
        padding: 2px 6px;
        border-radius: 3px;
        display: inline-block;
        min-width: 20px;
        text-align: center;
      }

      .status-normal {
        background-color: #e8f5e8;
        color: #2d5a2d;
      }

      .status-warning {
        background-color: #fff3cd;
        color: #856404;
      }

      .status-danger {
        background-color: #f8d7da;
        color: #721c24;
      }

      /* 紧凑的可折叠内容 */
      .collapsible-content.active {
        padding: 8px;
      }

      .collapsible-section {
        margin-bottom: 10px;
      }

      .collapsible-header {
        padding: 10px;
        font-size: 0.9rem;
      }

      .nested-collapsible-header {
        padding: 8px 10px;
        font-size: 0.85rem;
      }

      /* 说明框样式优化 */
      .collapsible-content div[style*="background-color: #fff3cd"],
      .collapsible-content div[style*="background-color: #e8f5e8"],
      .collapsible-content div[style*="background-color: #e7f3fe"] {
        padding: 10px !important;
        margin-bottom: 10px !important;
        font-size: 0.8rem !important;
        line-height: 1.4 !important;
      }

      /* 表格容器优化 */
      .report-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
      }
    }

    /* 超小屏幕优化 */
    @media (max-width: 480px) {
      .report-table {
        font-size: 0.75rem;
        margin: 8px 0;
      }

      .report-table th {
        padding: 4px 6px;
        font-size: 0.7rem;
      }

      .report-table td {
        padding: 4px 6px;
        font-size: 0.7rem;
      }

      .status-normal, .status-warning, .status-danger {
        font-size: 0.7rem;
        padding: 1px 4px;
        min-width: 18px;
      }

      .collapsible-content.active {
        padding: 6px;
      }

      .collapsible-header {
        padding: 8px;
        font-size: 0.85rem;
      }

      .nested-collapsible-header {
        padding: 6px 8px;
        font-size: 0.8rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="report-page">
      <div class="report-header">
        <div class="report-header-content">
          <img src="../assets/images/logo_new.jpg" alt="系统Logo" class="logo">
          <h1>健康检测报告</h1>
          <div class="report-date" id="report-date"></div>
        </div>
      </div>

      <div class="user-info-card">
        <div class="user-info-title">
          <i class="fas fa-user-circle"></i> 用户基本信息
        </div>
        <div class="user-info-grid" id="user-info">
          <!-- 用户信息将在这里动态加载 -->
        </div>
      </div>

      <div class="report-container" id="report-container">
        <!-- 报告内容将在这里动态加载 -->
      </div>

      <div class="report-actions">
        <button class="btn btn-outline report-action-btn" onclick="window.location.href='index.html'">
          <i class="fas fa-arrow-left"></i> 返回查询页面
        </button>
        <button class="btn report-action-btn" onclick="window.print()">
          <i class="fas fa-print"></i> 打印报告
        </button>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="footer">
    <div class="footer-content">
      <p class="footer-text">© 2025 健康评估系统由上海康德怡科技版权提供，并结合deepseek大模型AI驱动的智能报告解读系统，提供个性化的健康解决方案，提升生活质量</p>
      <p class="footer-text">提供全面的健康检测解决方案</p>
    </div>
  </footer>

  <script src="../utils/json-handler.js"></script>
  <script src="../utils/sync-service.js"></script>
  <script>
    // const API_BASE_URL = 'http://localhost:3005'; // 旧的固定地址
    const API_BASE_URL = window.location.origin; // 根据当前页面URL动态生成

    // 获取报告数据
    async function getReportData(phone, name) {
      console.log('开始获取报告数据...');

      if (!phone || !name) {
        console.error('缺少必要的用户信息，无法获取报告');
        return null;
      }

      try {
        // 直接通过API获取报告数据
        console.log(`通过API获取用户 ${name} (${phone}) 的报告数据`);
        const startTime = performance.now();
        const response = await fetch(`${API_BASE_URL}/api/reports/${phone}/${name}`);
        const endTime = performance.now();
        console.log(`API /api/reports call took ${endTime - startTime} milliseconds.`);

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            console.log('成功获取报告数据:', result.data);
            return result.data;
          } else {
            console.warn('API返回错误 (reports):', result.message, 'Input:', {phone, name});
            return null;
          }
        } else {
          const errorText = await response.text();
          console.error('API请求失败 (reports):', response.status, response.statusText, 'Response Text:', errorText, 'Input:', {phone, name});
          return null;
        }
      } catch (error) {
        console.error('获取报告数据 fetch 失败 (reports):', error, 'Input:', {phone, name});
        return null;
      }
    }

    // 根据生日计算年龄
    function calculateAge(birthday) {
      if (!birthday || birthday.length !== 8) {
        return 30;
      }

      try {
        const year = parseInt(birthday.substring(0, 4));
        const month = parseInt(birthday.substring(4, 6)) - 1;
        const day = parseInt(birthday.substring(6, 8));

        const birthDate = new Date(year, month, day);
        const today = new Date();

        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          age--;
        }

        return age;
      } catch (error) {
        console.warn('计算年龄失败:', error);
        return 30;
      }
    }

    // 页面加载时获取报告数据
    document.addEventListener('DOMContentLoaded', async function() {
      // 从URL获取用户信息
      const urlParams = new URLSearchParams(window.location.search);
      const phone = urlParams.get('phone');
      const name = urlParams.get('name');

      if (!phone || !name) {
        alert('缺少必要的用户信息，无法显示报告');
        window.location.href = 'index.html';
        return;
      }

      // 定义userData变量，确保它在整个函数作用域内可用
      let userData = {};

      try {
        console.log('通过API获取用户数据');
        const userStartTime = performance.now();
        const userResponse = await fetch(`${API_BASE_URL}/api/users/${phone}/${name}`);
        const userEndTime = performance.now();
        console.log(`API /api/users call took ${userEndTime - userStartTime} milliseconds.`);

        if (userResponse.ok) {
          const userResult = await userResponse.json();
          if (userResult.success) {
            userData = userResult.data;
            console.log('成功获取用户数据:', userData);
          } else {
            console.warn('API返回错误 (users):', userResult.message, 'Input:', {phone, name});
          }
        } else {
          const userErrorText = await userResponse.text();
          console.error('API请求失败 (users):', userResponse.status, userResponse.statusText, 'Response Text:', userErrorText, 'Input:', {phone, name});
        }
        console.log('准备显示用户信息 (after user data fetch attempt)');
      } catch (error) {
        console.error('获取用户数据 fetch 失败 (users):', error, 'Input:', {phone, name});
        // In case of user data fetch failure, still attempt to display basic info if reportData is later fetched
        // displayUserInfo({}, name, phone); // This might be redundant if reportData fetch also fails
      }

      // 显示加载中提示
      const reportContainer = document.getElementById('report-container');
      reportContainer.innerHTML = `
        <div class="loading-message">
          <div class="loading-spinner"></div>
          <h3>Synchronizing Report Data</h3>
          <p>Please wait, retrieving your health report from server...</p>
        </div>
      `;

      // 获取报告数据
      console.log(`开始获取用户 ${name} (${phone}) 的报告数据`);
      const reportData = await getReportData(phone, name);
      console.log('获取到的报告数据:', reportData);

      // 处理并显示报告
      if (reportData) {
        console.log('开始处理并显示报告数据');
        try {
          // 确保reportData是一个有效的对象
          if (typeof reportData === 'object' && reportData !== null) {
            // 更新用户信息显示，传入报告数据
            // 确保userData存在，如果不存在则使用空对象
            displayUserInfo(userData || {}, name, phone, reportData);

            // 处理并显示报告数据
            processAndDisplayReport(reportData);
            console.log('报告数据处理完成');
          } else {
            throw new Error('报告数据格式无效');
          }
        } catch (processError) {
          console.error('处理报告数据时出错:', processError);
          reportContainer.innerHTML = `
            <div class="error-message">
              <h2><i class="fas fa-exclamation-triangle"></i> 处理报告数据失败</h2>
              <p>错误信息: ${processError.message || '未知错误'}</p>
              <p>请稍后再试或联系客服。</p>
              <p style="margin-top: 15px; font-size: 0.9rem; color: #666;">
                <i class="fas fa-info-circle"></i> 技术信息: 处理报告数据时发生错误，请刷新页面重试。
              </p>
            </div>
          `;
        }
      } else {
        console.warn('未找到报告数据，显示错误提示');

        // 确保显示用户信息（使用已定义的userData变量）
        displayUserInfo(userData || {}, name, phone);

        // 显示未找到数据的提示
        reportContainer.innerHTML = `
          <div class="error-message">
            <h2><i class="fas fa-exclamation-triangle"></i> 未找到体检报告数据</h2>
            <p>体检报告数据只能从服务器同步获取，不会在创建用户时自动生成。</p>
            <p>可能的原因：</p>
            <ul>
              <li>您的体检报告尚未生成</li>
              <li>服务器无法获取您的用户数据，无法生成体检报告</li>
              <li>您输入的信息有误</li>
            </ul>
            <p>服务器会每30分钟自动尝试同步一次所有用户的体检数据，但只有在成功获取到用户数据时才会生成体检报告。</p>
            <p>如果您确认已正确填写用户信息，请稍后再查询。</p>
            <p style="margin-top: 15px; font-size: 0.9rem; color: #666;">
              <i class="fas fa-info-circle"></i> 提示：只有在用户信息完整的情况下，系统才会生成体检报告。
            </p>
          </div>
        `;
      }
    });

    /**
     * 显示用户信息
     * @param {Object} userData - 用户表单数据
     * @param {string} name - 用户姓名
     * @param {string} phone - 用户手机号
     * @param {Object} reportData - 报告数据（可选）
     */
    function displayUserInfo(userData, name, phone, reportData = null) {
      const userInfoDiv = document.getElementById('user-info');
      const reportDateDiv = document.getElementById('report-date');

      // 设置报告日期
      const now = new Date();

      // 格式化日期和时间，使其更加清晰易读
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      // 使用更清晰的格式显示时间，添加图标
      reportDateDiv.innerHTML = `
        <i class="fas fa-calendar-alt"></i>
        <span>报告生成时间: ${year}-${month}-${day} ${hours}:${minutes}:${seconds}</span>
      `;

      // 构建用户信息HTML
      let userInfoHTML = `
        <div class="user-info-item">
          <div class="user-info-label">姓名</div>
          <div class="user-info-value">${name}</div>
        </div>
        <div class="user-info-item">
          <div class="user-info-label">手机号</div>
          <div class="user-info-value">${phone}</div>
        </div>
      `;

      // 添加性别信息（优先使用用户表单数据，如果没有则使用报告数据）
      const gender = userData.gender || (reportData && reportData.sex) || null;
      if (gender) {
        userInfoHTML += `
          <div class="user-info-item">
            <div class="user-info-label">性别</div>
            <div class="user-info-value">${gender}</div>
          </div>
        `;
      }

      // 添加出生日期信息
      if (userData.birthday) {
        // 格式化生日 YYYYMMDD -> YYYY-MM-DD
        const formattedBirthday = userData.birthday.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3');
        userInfoHTML += `
          <div class="user-info-item">
            <div class="user-info-label">出生日期</div>
            <div class="user-info-value">${formattedBirthday}</div>
          </div>
        `;
      }

      // 添加血型信息
      if (userData.blood_type) {
        userInfoHTML += `
          <div class="user-info-item">
            <div class="user-info-label">血型</div>
            <div class="user-info-value">${userData.blood_type}</div>
          </div>
        `;
      }

      // 添加检测类型信息
      if (userData.test_type) {
        userInfoHTML += `
          <div class="user-info-item">
            <div class="user-info-label">检测类型</div>
            <div class="user-info-value">${userData.test_type}</div>
          </div>
        `;
      }

      // 添加AI相关信息
      if (userData.ai_fix_times && userData.ai_fix_times !== '0') {
        userInfoHTML += `
          <div class="user-info-item">
            <div class="user-info-label">AI修复次数</div>
            <div class="user-info-value">${userData.ai_fix_times}次</div>
          </div>
        `;
      }

      if (userData.ai_report && userData.ai_report === '是') {
        userInfoHTML += `
          <div class="user-info-item">
            <div class="user-info-label">AI报告</div>
            <div class="user-info-value">是</div>
          </div>
        `;
      }

      // 从reportData中添加年龄和检测时间（如果可用）
      if (reportData) {
        if (reportData.age) {
          userInfoHTML += `
            <div class="user-info-item">
              <div class="user-info-label">年龄</div>
              <div class="user-info-value">${reportData.age}</div>
            </div>
          `;
        }
        if (reportData.time) {
          // 尝试格式化检测时间
          const formattedTime = formatDateTime(reportData.time);
          userInfoHTML += `
            <div class="user-info-item">
              <div class="user-info-label">检测时间</div>
              <div class="user-info-value">${formattedTime}</div>
            </div>
          `;
        }
      }

      userInfoDiv.innerHTML = userInfoHTML;
    }

    /**
     * 处理并显示报告数据
     */
    function processAndDisplayReport(reportData) {
      const reportContainer = document.getElementById('report-container');

      // 检查报告数据是否有效
      if (!reportData || typeof reportData !== 'object') {
        reportContainer.innerHTML = `
          <div class="error-message">
            <h2><i class="fas fa-exclamation-triangle"></i> Invalid Report Data</h2>
            <p>Unable to process report data. Please contact administrator.</p>
            <p style="margin-top: 15px; font-size: 0.9rem; color: #666;">报告数据无效，请联系管理员。</p>
          </div>
        `;
        return;
      }

      console.log('处理报告数据:', reportData);

      // 构建报告HTML
      let reportHTML = '';

      // 辅助函数：创建可折叠区域的头部
      function createCollapsibleHeader(title, iconClass, sectionId, additionalHeaderClass = '', iconColor = null) {
        const iconStyle = iconColor ? `style="color: ${iconColor}; margin-right: 8px;"` : 'style="margin-right: 8px;"'; // Added margin-right to icon
        return `
          <div class="collapsible-header ${additionalHeaderClass}" data-target="${sectionId}">
            <div style="display: flex; align-items: center;"><i class="${iconClass}" ${iconStyle}></i> ${title}</div>
            <i class="fas fa-chevron-right arrow"></i>
          </div>
        `;
      }

      // 添加报告声明书部分 (可折叠)
      reportHTML += `
        <div class="collapsible-section">
          ${createCollapsibleHeader('报告声明书', 'fas fa-gavel', 'statement-content')}
          <div class="collapsible-content" id="statement-content">
            <div style="padding: 15px; font-size: 0.9rem; line-height: 1.8;">
              <ol style="margin-left: 20px; padding-left: 0;">
                <li>本设备定位为生命科学仪器，系统及设备为电子设备，并非医疗设备，其系统测试结果非医学诊断医疗用途，不具备医学之证明材料；</li>
                <li>本AI健康风险评估报告仅供受检者参考，其结论不能替代正规的医学检测结果；</li>
                <li>本报告是健康检测预防为主，应用于健康管理之健康评估及养生、运动指导评估；</li>
                <li>本报告提供各方面的参考知识及经验，让被测者直观及有所警示，加强对运动、日常保健之重视，以供参考执行；</li>
                <li>本系统以及报告涉及的所有资料：商标、标志、图像、短片、声音档案、链接、以及其他资料，仅供参考之用；</li>
                <li>本公司为每一个数据档案建档储存并且保密，但不会为数据遗失、黑客攻击、不可预知因素的数据丢失及数据传播负责任；</li>
                <li>本系统尽力确保资料的准确性、正确性，但不会明示或隐含保证该资料均为正确无误，因提供学习参考之用本公司不对任何错误或遗漏承担责任；</li>
                <li>在未经本公司明确授权的合作伙伴不正当及不正确使用，本公司概不负责；</li>
                <li>在未经本公司授权同意之情况下，本系统以及使用说明中的图文资料，不可被使用、复印、销售、传送、修改、发表、储存或以其他方式利用作任何用途；</li>
              </ol>
              <ul style="margin-left: 20px; padding-left: 0; list-style-type: disc; margin-top: 15px;">
                <li>本报告结合人工智能所产生的报告，预测健康趋势。</li>
                <li>器官能量图，如有5、6级符号出现则说明您的健康状况需要引起警惕。</li>
              </ul>
            </div>
          </div>
        </div>
      `;

      // 添加注意事项部分
      reportHTML += `
        <div class="collapsible-section">
          ${createCollapsibleHeader('注意事项', 'fas fa-exclamation-circle', 'precautions-content')}
          <div class="collapsible-content" id="precautions-content">
            <div style="padding: 15px;">
              <ul style="list-style-type: disc; margin-left: 20px; padding-left: 0; line-height: 1.8; font-size: 0.9rem;">
                <li>摘除身上具有能量的珠宝、玉石等、具有磁性的手机、电子钥匙等、金属物品(手表、眼镜等)。</li>
                <li>在检测前2小时内饮用茶叶、咖啡刺激性的饮料，对检测消化系统会有影响。</li>
                <li>近期有使用保健品，药物，理疗等，会对检测结果有掩盖。</li>
                <li>体内有金属植入的(如心脏支架等)对该部位检测有影响。</li>
                <li>做过器官切除的，因为医学上的幽灵假肢现象，依然可检测到相关问题。</li>
                <li>检测过程中双手双脚不要交叉，保持放松状态。</li>
              </ul>
            </div>
          </div>
        </div>
      `;

      // 添加不建议做检测人群部分
      reportHTML += `
        <div class="collapsible-section">
          ${createCollapsibleHeader('不建议做检测人群', 'fas fa-user-times', 'not-recommended-content')}
          <div class="collapsible-content" id="not-recommended-content">
            <div style="padding: 15px;">
              <ul style="list-style-type: disc; margin-left: 20px; padding-left: 0; line-height: 1.8; font-size: 0.9rem; color: var(--danger-color);">
                <li>怀孕者；</li>
                <li>7岁以下儿童；进行过器官移植者；</li>
                <li>有严重累积性病状者(如：癫痫)；</li>
                <li>患有严重心理疾病者(精神病，忧郁症，躁狂症)；</li>
                <li>患有重度急性发生肌张力过高者</li>
              </ul>
            </div>
          </div>
        </div>
      `;

      // 添加组织器官部分 (如果organ数据存在)
      if (reportData.organ) {
        reportHTML += `
          <div class="collapsible-section">
            ${createCollapsibleHeader('组织器官', 'fas fa-brain', 'tissue-organ-content')}
            <div class="collapsible-content" id="tissue-organ-content">
              <div style="padding: 15px;">
                <p style="text-align: center; font-weight: bold; color: var(--primary-color); font-size: 1.1rem; margin-bottom: 10px;">组织器官D值越大结果越差</p>
                <div style="background-color: #e7f3fe; color: #31708f; border: 1px solid #bce8f1; padding: 15px; border-radius: var(--border-radius); margin-bottom: 20px; font-size:0.9rem; line-height:1.6;">
                  包括各健康组织结构特属频谱的比较样本，比较样本列表的是依照移除受检部位频谱相似度后的频率比较做排列。健康组织的最初信号和输出信号几乎不分离（红色和蓝色线条重叠）。有机体器官组织和受检部位的频谱差异愈小，表示此组织愈健康；差异愈大，表示此组织遭受破坏的程度愈高。数值大于2.0说明该部位有症状，数值在1.0-2.0之间的需要去进行人为的判断。
                </div>`;

        if (reportData.organ.reference_range && reportData.organ.reference_range.length > 0) {
          reportHTML += `
            <h4 style="margin-top: 20px; margin-bottom:10px; color: var(--primary-color); border-bottom: 1px solid var(--border-color); padding-bottom: 5px;">参考范围</h4>
            <div class="report-table-container">
              <table class="report-table">
                <thead><tr><th>D值</th><th>状态</th></tr></thead>
                <tbody>`;
          reportData.organ.reference_range.forEach(range => {
            const statusClass = range.resule === '重' ? 'status-danger' : range.resule === '中' ? 'status-warning' : 'status-normal';
            reportHTML += `<tr><td>${range.d_value}</td><td class="${statusClass}">${range.resule}</td></tr>`;
          });
          reportHTML += `</tbody></table></div>`;
        }

        if (reportData.organ.filter_project && reportData.organ.filter_project.length > 0) {
          reportHTML += `
            <h4 style="margin-top: 20px; margin-bottom:10px; color: var(--primary-color); border-bottom: 1px solid var(--border-color); padding-bottom: 5px;">筛选项目</h4>
            <div class="report-table-container">
              <table class="report-table">
                <thead><tr><th>筛选项目</th><th>D值</th><th>对比结果</th></tr></thead>
                <tbody>`;
          reportData.organ.filter_project.forEach(item => {
            const statusClass = item.resule === '重' ? 'status-danger' : item.resule === '中' ? 'status-warning' : 'status-normal';
            reportHTML += `<tr><td>${item.name}</td><td>${item.d_value}</td><td class="${statusClass}">${item.resule}</td></tr>`;
          });
          reportHTML += `</tbody></table></div>`;
        }
          reportHTML += `</tbody></table>`;
        }
        reportHTML += `</div></div></div>`; // Close content padding, collapsible-content, collapsible-section
      }

      // 添加生理系统部分（如果存在）
      if (reportData.Physiological_system) {
        const mainPhysiologicalSystemId = 'physiological-systems-main-content';
        reportHTML += `
          <div class="collapsible-section">
            ${createCollapsibleHeader('生理系统', 'fas fa-brain', mainPhysiologicalSystemId, '', '#dc3545')}
            <div class="collapsible-content" id="${mainPhysiologicalSystemId}">
              <div style="padding-top: 5px;">
        `;

        const physiologicalSystemsConfig = [
          { key: 'respiratory_system', name: '呼吸系统', icon: 'fas fa-lungs', color: '#28a745' },
          { key: 'sensory_system', name: '感觉系统', icon: 'fas fa-eye', color: '#28a745' },
          { key: 'urogenital_system', name: '泌尿生殖系统', icon: 'fas fa-venus-mars', color: '#17a2b8' },
          { key: 'urinary_system', name: '泌尿系统', icon: 'fas fa-tint', color: '#17a2b8' },
          { key: 'digestive_system', name: '消化系统', icon: 'fas fa-utensils', color: '#fd7e14' },
          { key: 'musculoskeletal_system', name: '肌肉骨骼系统', icon: 'fas fa-bone', color: '#6f42c1' }
        ];

        physiologicalSystemsConfig.forEach(sysConfig => {
          const systemData = reportData.Physiological_system[sysConfig.key];
          if (systemData) {
            const systemId = `physio-sys-${sysConfig.key}-content`;
            reportHTML += `
              <div class="collapsible-section" style="margin: 0 5px 5px 5px;">
                ${createCollapsibleHeader(sysConfig.name, sysConfig.icon, systemId, 'nested-collapsible-header', sysConfig.color)}
                <div class="collapsible-content" id="${systemId}">
                  <div style="padding: 10px;">
            `;

            // 为呼吸系统添加特殊的病理分析描述
            if (sysConfig.key === 'respiratory_system') {
              reportHTML += `
                <div style="background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 15px; border-radius: var(--border-radius); margin-bottom: 15px; font-size: 0.9rem; line-height: 1.6;">
                  <h6 style="margin: 0 0 10px 0; color: #856404; font-weight: 600;"><i class="fas fa-info-circle" style="margin-right: 5px;"></i>病理分析说明</h6>
                  <p style="margin: 0 0 8px 0;"><strong>D值越小结果越差</strong></p>
                  <p style="margin: 0;">标准和各目标受检部位所收集到此项目的频谱差(D）值。如果D值小于0.425，意味此受检部位所收集到此项目的频谱和标准资料库相似度超过95%，此项目会呈现红色，如果此项目D值超过0.750表示与标准库的相似度大于65%。</p>
                </div>
              `;
            }

            // 为感觉系统添加特殊的病理分析描述
            if (sysConfig.key === 'sensory_system') {
              reportHTML += `
                <div style="background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 15px; border-radius: var(--border-radius); margin-bottom: 15px; font-size: 0.9rem; line-height: 1.6;">
                  <h6 style="margin: 0 0 10px 0; color: #856404; font-weight: 600;"><i class="fas fa-info-circle" style="margin-right: 5px;"></i>病理分析说明</h6>
                  <p style="margin: 0 0 8px 0;"><strong>D值越小结果越差</strong></p>
                  <p style="margin: 0;">标准和各目标受检部位所收集到此项目的频谱差(D）值。如果D值小于0.425，意味此受检部位所收集到此项目的频谱和标准资料库相似度超过95%，此项目会呈现红色，如果此项目D值超过0.750表示与标准库的相似度大于65%。</p>
                </div>
              `;
            }

            // 为泌尿生殖系统添加特殊的病理分析描述
            if (sysConfig.key === 'urogenital_system') {
              reportHTML += `
                <div style="background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 15px; border-radius: var(--border-radius); margin-bottom: 15px; font-size: 0.9rem; line-height: 1.6;">
                  <h6 style="margin: 0 0 10px 0; color: #856404; font-weight: 600;"><i class="fas fa-info-circle" style="margin-right: 5px;"></i>病理分析说明</h6>
                  <p style="margin: 0 0 8px 0;"><strong>D值越小结果越差</strong></p>
                  <p style="margin: 0;">标准和各目标受检部位所收集到此项目的频谱差(D）值。如果D值小于0.425，意味此受检部位所收集到此项目的频谱和标准资料库相似度超过95%，此项目会呈现红色，如果此项目D值超过0.750表示与标准库的相似度大于65%。</p>
                </div>
              `;
            }

            // 为泌尿系统添加特殊的病理分析描述
            if (sysConfig.key === 'urinary_system') {
              reportHTML += `
                <div style="background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 15px; border-radius: var(--border-radius); margin-bottom: 15px; font-size: 0.9rem; line-height: 1.6;">
                  <h6 style="margin: 0 0 10px 0; color: #856404; font-weight: 600;"><i class="fas fa-info-circle" style="margin-right: 5px;"></i>病理分析说明</h6>
                  <p style="margin: 0 0 8px 0;"><strong>D值越小结果越差</strong></p>
                  <p style="margin: 0;">标准和各目标受检部位所收集到此项目的频谱差(D）值。如果D值小于0.425，意味此受检部位所收集到此项目的频谱和标准资料库相似度超过95%，此项目会呈现红色，如果此项目D值超过0.750表示与标准库的相似度大于65%。</p>
                </div>
              `;
            }

            // 为消化系统添加特殊的病理分析描述
            if (sysConfig.key === 'digestive_system') {
              reportHTML += `
                <div style="background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 15px; border-radius: var(--border-radius); margin-bottom: 15px; font-size: 0.9rem; line-height: 1.6;">
                  <h6 style="margin: 0 0 10px 0; color: #856404; font-weight: 600;"><i class="fas fa-info-circle" style="margin-right: 5px;"></i>病理分析说明</h6>
                  <p style="margin: 0 0 8px 0;"><strong>D值越小结果越差</strong></p>
                  <p style="margin: 0;">标准和各目标受检部位所收集到此项目的频谱差(D）值。如果D值小于0.425，意味此受检部位所收集到此项目的频谱和标准资料库相似度超过95%，此项目会呈现红色，如果此项目D值超过0.750表示与标准库的相似度大于65%。</p>
                </div>
              `;
            }

            // 为肌肉骨骼系统添加特殊的病理分析描述
            if (sysConfig.key === 'musculoskeletal_system') {
              reportHTML += `
                <div style="background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; padding: 15px; border-radius: var(--border-radius); margin-bottom: 15px; font-size: 0.9rem; line-height: 1.6;">
                  <h6 style="margin: 0 0 10px 0; color: #856404; font-weight: 600;"><i class="fas fa-info-circle" style="margin-right: 5px;"></i>病理分析说明</h6>
                  <p style="margin: 0 0 8px 0;"><strong>D值越小结果越差</strong></p>
                  <p style="margin: 0;">标准和各目标受检部位所收集到此项目的频谱差(D）值。如果D值小于0.425，意味此受检部位所收集到此项目的频谱和标准资料库相似度超过95%，此项目会呈现红色，如果此项目D值超过0.750表示与标准库的相似度大于65%。</p>
                </div>
              `;
            }

            let hasDataForSystem = false;
            // Reference Range Table for this system
            if (systemData.reference_range && systemData.reference_range.length > 0) {
              hasDataForSystem = true;
              reportHTML += `
                    <h5 style="margin-top: 5px; margin-bottom:5px; color: var(--primary-color); font-size: 0.9rem;">参考范围</h5>
                    <div class="report-table-container">
                      <table class="report-table">
                        <thead><tr><th>D值</th><th>状态</th></tr></thead>
                        <tbody>`;
              systemData.reference_range.forEach(range => {
                const statusClass = range.resule === '重' ? 'status-danger' : range.resule === '中' ? 'status-warning' : 'status-normal';
                reportHTML += `<tr><td>${range.d_value}</td><td class="${statusClass}">${range.resule}</td></tr>`;
              });
              reportHTML += `</tbody></table></div>`;
            }

            // Filter Project Table for this system
            if (systemData.filter_project && systemData.filter_project.length > 0) {
              hasDataForSystem = true;
              reportHTML += `
                    <h5 style="margin-top: ${ (systemData.reference_range && systemData.reference_range.length > 0) ? '15px' : '5px' }; margin-bottom:5px; color: var(--primary-color); font-size: 0.9rem;">筛选项目</h5>
                    <div class="report-table-container">
                      <table class="report-table">
                        <thead><tr><th>项目名称</th><th>健康指数</th><th>健康状态</th></tr></thead>
                        <tbody>`;
              systemData.filter_project.forEach(item => {
                const statusClass = item.resule === '重' ? 'status-danger' : item.resule === '中' ? 'status-warning' : 'status-normal';
                reportHTML += `<tr><td>${item.name}</td><td>${item.d_value}</td><td class="${statusClass}">${item.resule}</td></tr>`;
              });
              reportHTML += `</tbody></table></div>`;
            }
            
            if (!hasDataForSystem) {
                reportHTML += `<p style="font-size:0.9rem; color: var(--light-text); padding:10px 0;">此系统无详细数据。</p>`;
            }

            reportHTML += `
                  </div>
                </div>
              </div>
            `;
          }
        });

        reportHTML += `
              </div>
            </div>
          </div>
        `;
      }

      // 添加微生物部分（如果存在）
      if (reportData.microbial_worms && reportData.microbial_worms.filter_project && reportData.microbial_worms.filter_project.length > 0) {
        reportHTML += `
          <div class="collapsible-section">
            ${createCollapsibleHeader('微生物检测', 'fas fa-bug', 'microbial-content')}
            <div class="collapsible-content" id="microbial-content">
              <div style="padding: 15px;">
                <div style="background-color: #e8f5e8; color: #2d5a2d; border: 1px solid #c3e6c3; padding: 15px; border-radius: var(--border-radius); margin-bottom: 15px; font-size: 0.9rem; line-height: 1.6;">
                  <h6 style="margin: 0 0 10px 0; color: #2d5a2d; font-weight: 600;"><i class="fas fa-microscope" style="margin-right: 5px;"></i>微生物和蠕虫分析说明</h6>
                  <p style="margin: 0;">微生物及蠕虫分析资料库清单记录了细菌、病毒、立克次体、微胞浆体、真菌、蠕虫之类的感染原的名称及特属频谱库。</p>
                </div>
                <div class="report-table-container">
                  <table class="report-table">
                    <thead><tr><th>微生物名称</th><th>健康指数</th><th>健康状态</th></tr></thead>
                    <tbody>`;
        reportData.microbial_worms.filter_project.forEach(item => {
          const statusClass = item.resule === '重' ? 'status-danger' : item.resule === '中' ? 'status-warning' : 'status-normal';
          reportHTML += `<tr><td>${item.name}</td><td>${item.d_value}</td><td class="${statusClass}">${item.resule}</td></tr>`;
        });
        reportHTML += `</tbody></table></div></div></div></div>`;
      }

      // 添加食物建议部分（如果存在）
      if (reportData.foods && reportData.foods.filter_project && reportData.foods.filter_project.length > 0) {
        reportHTML += `
          <div class="collapsible-section">
            ${createCollapsibleHeader('食物建议', 'fas fa-apple-alt', 'food-advice-content')}
            <div class="collapsible-content" id="food-advice-content">
              <div style="padding: 15px;">
                <div class="report-table-container">
                  <table class="report-table">
                    <thead><tr><th>食物名称</th><th>健康指数</th></tr></thead>
                    <tbody>`;
        reportData.foods.filter_project.forEach(item => {
          reportHTML += `<tr><td>${item.name}</td><td>${item.d_value}</td></tr>`;
        });
        reportHTML += `</tbody></table></div></div></div></div>`;
      }

      // 添加矿物质建议部分（如果存在）
      if (reportData.mineral && reportData.mineral.filter_project && reportData.mineral.filter_project.length > 0) {
        reportHTML += `
          <div class="collapsible-section">
            ${createCollapsibleHeader('矿物质建议', 'fas fa-gem', 'mineral-advice-content')}
            <div class="collapsible-content" id="mineral-advice-content">
              <div style="padding: 15px;">
                <div class="report-table-container">
                  <table class="report-table">
                    <thead><tr><th>矿物质名称</th><th>健康指数</th></tr></thead>
                    <tbody>`;
        reportData.mineral.filter_project.forEach(item => {
          reportHTML += `<tr><td>${item.name}</td><td>${item.d_value}</td></tr>`;
        });
        reportHTML += `</tbody></table></div></div></div></div>`;
      }

      // 添加健康建议部分
      reportHTML += `
        <div class="collapsible-section">
          ${createCollapsibleHeader('健康建议', 'fas fa-notes-medical', 'health-advice-content')}
          <div class="collapsible-content" id="health-advice-content">
            <div style="padding: 15px;">
              <div class="symptom-info">
                <div class="symptom-title">生活习惯建议</div>
                <p>根据您的健康检测结果，建议您保持良好的生活习惯，包括规律作息、均衡饮食、适量运动等。</p>
              </div>
              <div class="symptom-info" style="margin-top: 15px;">
                <div class="symptom-title">定期检查建议</div>
                <p>建议您定期进行健康检查，特别关注${getHighRiskOrgans(reportData)}等方面的健康状况。</p>
              </div>
            </div>
          </div>
        </div>
      `;

      // 显示报告内容
      reportContainer.innerHTML = reportHTML;

      // 添加折叠功能
      const collapsibles = reportContainer.querySelectorAll('.collapsible-header');
      collapsibles.forEach(header => {
        header.addEventListener('click', function() {
          this.classList.toggle('active');
          const contentId = this.dataset.target;
          const content = document.getElementById(contentId);
          if (content) {
            content.classList.toggle('active');
          }
        });
      });

      // 默认展开报告声明书、注意事项、不建议人群和组织器官部分
      const sectionsToExpand = [
        'statement-content',
        'precautions-content',
        'not-recommended-content',
        'tissue-organ-content'
      ];

      sectionsToExpand.forEach(id => {
        const header = reportContainer.querySelector(`.collapsible-header[data-target="${id}"]`);
        const content = document.getElementById(id);
        if (header && content) {
          header.classList.add('active');
          content.classList.add('active');
        }
      });
    }

    /**
     * 格式化日期时间为更易读的格式
     * @param {string|Date} dateTimeInput - 日期时间字符串或Date对象
     * @returns {string} 格式化后的日期时间字符串
     */
    function formatDateTime(dateTimeInput) {
      try {
        // 如果输入已经是字符串格式，并且看起来已经格式化好了，直接返回
        if (typeof dateTimeInput === 'string') {
          // 检查是否已经是格式化好的日期时间字符串（YYYY-MM-DD HH:MM:SS）
          if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateTimeInput)) {
            return dateTimeInput;
          }

          // 检查是否是ISO格式的日期时间字符串
          if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(dateTimeInput)) {
            const date = new Date(dateTimeInput);
            if (!isNaN(date.getTime())) {
              // 格式化日期和时间
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              const hours = String(date.getHours()).padStart(2, '0');
              const minutes = String(date.getMinutes()).padStart(2, '0');
              const seconds = String(date.getSeconds()).padStart(2, '0');

              return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            }
          }
        }

        // 尝试将输入转换为Date对象
        const date = new Date(dateTimeInput);

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          console.warn('无效的日期时间输入:', dateTimeInput);
          return String(dateTimeInput || '未知时间');
        }

        // 格式化日期和时间
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        // 返回格式化后的日期时间
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (error) {
        console.error('格式化日期时间失败:', error, dateTimeInput);
        return String(dateTimeInput || '未知时间');
      }
    }

    /**
     * 获取高风险器官列表
     */
    function getHighRiskOrgans(reportData) {
      let highRiskItems = [];

      // 检查主要器官部分
      if (reportData && reportData.organ && reportData.organ.filter_project) {
        // 筛选出健康状态为"重"的器官
        const highRiskOrgans = reportData.organ.filter_project
          .filter(organ => organ.resule === '重')
          .map(organ => organ.name);

        highRiskItems = [...highRiskItems, ...highRiskOrgans];
      }

      // 检查生理系统部分
      if (reportData && reportData.Physiological_system) {
        const systems = Object.keys(reportData.Physiological_system);

        for (const system of systems) {
          const systemData = reportData.Physiological_system[system];

          if (systemData && systemData.filter_project) {
            // 筛选出健康状态为"重"的项目
            const highRiskItems2 = systemData.filter_project
              .filter(item => item.resule === '重')
              .map(item => item.name);

            highRiskItems = [...highRiskItems, ...highRiskItems2];
          }
        }
      }

      // 检查微生物部分
      if (reportData && reportData.microbial_worms && reportData.microbial_worms.filter_project) {
        // 筛选出健康状态为"重"的微生物
        const highRiskMicrobes = reportData.microbial_worms.filter_project
          .filter(item => item.resule === '重')
          .map(item => item.name);

        highRiskItems = [...highRiskItems, ...highRiskMicrobes];
      }

      // 如果没有高风险项目，尝试获取中等风险项目
      if (highRiskItems.length === 0) {
        let mediumRiskItems = [];

        // 检查主要器官部分
        if (reportData && reportData.organ && reportData.organ.filter_project) {
          // 筛选出健康状态为"中"的器官
          const mediumRiskOrgans = reportData.organ.filter_project
            .filter(organ => organ.resule === '中')
            .map(organ => organ.name);

          mediumRiskItems = [...mediumRiskItems, ...mediumRiskOrgans];
        }

        // 检查生理系统部分
        if (reportData && reportData.Physiological_system) {
          const systems = Object.keys(reportData.Physiological_system);

          for (const system of systems) {
            const systemData = reportData.Physiological_system[system];

            if (systemData && systemData.filter_project) {
              // 筛选出健康状态为"中"的项目
              const mediumRiskItems2 = systemData.filter_project
                .filter(item => item.resule === '中')
                .map(item => item.name);

              mediumRiskItems = [...mediumRiskItems, ...mediumRiskItems2];
            }
          }
        }

        // 检查微生物部分
        if (reportData && reportData.microbial_worms && reportData.microbial_worms.filter_project) {
          // 筛选出健康状态为"中"的微生物
          const mediumRiskMicrobes = reportData.microbial_worms.filter_project
            .filter(item => item.resule === '中')
            .map(item => item.name);

          mediumRiskItems = [...mediumRiskItems, ...mediumRiskMicrobes];
        }

        if (mediumRiskItems.length === 0) {
          return "全身各系统";
        }

        // 最多显示3个中等风险项目
        return mediumRiskItems.slice(0, 3).join('、');
      }

      // 最多显示3个高风险项目
      return highRiskItems.slice(0, 3).join('、');
    }
  </script>
</body>
</html>
