<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>报告查询 - 健康评估系统</title>
  <link rel="stylesheet" href="../assets/css/main.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    .search-card {
      max-width: 500px;
      margin: 0 auto;
    }

    .search-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .search-header img.logo {
      max-width: 200px;
      display: block;
      margin: 0 auto 20px auto;
    }

    .search-header h1 {
      color: var(--primary-color);
      margin-bottom: 10px;
    }

    .search-header p {
      color: var(--light-text);
    }

    .search-illustration {
      text-align: center;
      margin-bottom: 30px;
    }

    .search-illustration i {
      font-size: 5rem;
      color: var(--primary-color);
      opacity: 0.8;
    }

    .search-form {
      margin-bottom: 30px;
    }

    .recent-searches {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid var(--border-color);
    }

    .recent-searches h3 {
      font-size: 1rem;
      color: var(--light-text);
      margin-bottom: 15px;
    }

    .recent-search-item {
      display: flex;
      align-items: center;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 10px;
      background-color: #f5f5f5;
      cursor: pointer;
      transition: var(--transition);
    }

    .recent-search-item:hover {
      background-color: #e9e9e9;
    }

    .recent-search-info {
      flex: 1;
    }

    .recent-search-name {
      font-weight: 500;
    }

    .recent-search-phone {
      font-size: 0.9rem;
      color: var(--light-text);
    }

    .recent-search-action {
      color: var(--primary-color);
    }

    .search-tips {
      margin-top: 30px;
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: var(--border-radius);
      border-left: 4px solid var(--primary-color);
    }

    .search-tips h3 {
      font-size: 1rem;
      color: var(--primary-color);
      margin-bottom: 10px;
    }

    .search-tips ul {
      margin-left: 20px;
      line-height: 1.6;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="card search-card">
      <div class="search-header">
        <img src="../assets/images/logo_new.jpg" alt="系统Logo" class="logo">
        <h1>报告查询</h1>
        <p>请输入您的信息查询健康检测报告</p>
      </div>

      <div class="search-form">
        <div class="form-group">
          <label for="phone" class="form-label">手机号码</label>
          <div class="input-group" style="display: flex;">
            <input type="tel" id="phone" class="form-input" placeholder="填写11位手机号码" pattern="^\d{11}$" required>
          </div>
        </div>

        <div class="form-group">
          <label for="name" class="form-label">姓名</label>
          <input type="text" id="name" class="form-input" placeholder="填写姓名">
        </div>

        <div id="error-msg" style="color: var(--danger-color); margin-bottom: 20px;"></div>

        <button class="btn" onclick="searchReport()" style="width: 100%;">
          <i class="fas fa-search"></i> 查询报告
        </button>
      </div>

      <div id="recent-searches" class="recent-searches" style="display: none;">
        <h3><i class="fas fa-history"></i> 最近查询</h3>
        <div id="recent-searches-list"></div>
      </div>

      <div class="search-tips">
        <h3><i class="fas fa-info-circle"></i> 查询提示</h3>
        <ul>
          <li>请确保输入的手机号码和姓名与填写信息时一致</li>
          <li>报告数据由服务器定期同步，如未找到报告，请稍后再试</li>
          <li>如有问题，请联系工作人员寻求帮助</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="footer">
    <div class="footer-content">
      <p class="footer-text">© 2025 健康评估系统由上海康德怡科技版权提供，并结合deepseek大模型AI驱动的智能报告解读系统，提供个性化的健康解决方案，提升生活质量</p>
      <p class="footer-text">提供全面的健康检测解决方案</p>
    </div>
  </footer>

  <script src="report.js"></script>
  <script>
    // 添加最近查询功能
    document.addEventListener('DOMContentLoaded', function() {
      // 从localStorage获取最近查询记录
      const recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');

      // 如果有最近查询记录，显示最近查询区域
      if (recentSearches.length > 0) {
        document.getElementById('recent-searches').style.display = 'block';

        // 显示最近查询记录
        const recentSearchesList = document.getElementById('recent-searches-list');
        recentSearchesList.innerHTML = '';

        // 只显示最近5条记录
        const recentItems = recentSearches.slice(0, 5);

        recentItems.forEach(item => {
          const searchItem = document.createElement('div');
          searchItem.className = 'recent-search-item';
          searchItem.innerHTML = `
            <div class="recent-search-info">
              <div class="recent-search-name">${item.name}</div>
              <div class="recent-search-phone">${item.phone}</div>
            </div>
            <div class="recent-search-action">
              <i class="fas fa-chevron-right"></i>
            </div>
          `;

          // 点击最近查询记录，自动填充表单并查询
          searchItem.addEventListener('click', function() {
            document.getElementById('phone').value = item.phone;
            document.getElementById('name').value = item.name;
            searchReport();
          });

          recentSearchesList.appendChild(searchItem);
        });
      }

      // 添加回车键监听
      document.getElementById('name').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          searchReport();
        }
      });

      document.getElementById('phone').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          document.getElementById('name').focus();
        }
      });
    });

    // 保存查询记录
    function saveSearchHistory(phone, name) {
      // 从localStorage获取最近查询记录
      const recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');

      // 检查是否已存在相同的记录
      const existingIndex = recentSearches.findIndex(item => item.phone === phone && item.name === name);

      // 如果已存在，删除旧记录
      if (existingIndex !== -1) {
        recentSearches.splice(existingIndex, 1);
      }

      // 添加新记录到开头
      recentSearches.unshift({
        phone,
        name,
        timestamp: new Date().toISOString()
      });

      // 只保留最近10条记录
      const limitedSearches = recentSearches.slice(0, 10);

      // 保存到localStorage
      localStorage.setItem('recentSearches', JSON.stringify(limitedSearches));
    }

    // 重写searchReport函数，添加保存查询记录功能
    const originalSearchReport = searchReport;
    searchReport = function() {
      const phone = document.getElementById('phone').value.trim();
      const name = document.getElementById('name').value.trim();

      // 验证输入
      if (phone && name) {
        // 保存查询记录
        saveSearchHistory(phone, name);
      }

      // 调用原始的searchReport函数
      originalSearchReport();
    };
  </script>
</body>
</html>
