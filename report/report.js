// const API_BASE_URL = 'http://localhost:3005'; // 旧的固定地址
const API_BASE_URL = window.location.origin; // 根据当前页面URL动态生成

/**
 * 查询报告
 */
async function searchReport() {
  const phone = document.getElementById('phone').value.trim();
  const name = document.getElementById('name').value.trim();
  const errorDiv = document.getElementById('error-msg');

  // 验证手机号
  if (!/^\d{11}$/.test(phone)) {
    errorDiv.innerText = "请输入正确的11位手机号码";
    return;
  }

  // 验证姓名
  if (!name) {
    errorDiv.innerText = "请输入姓名";
    return;
  }

  // 显示加载中
  errorDiv.innerText = "正在查询用户数据...";

  try {
    // 直接通过API查询用户数据
    console.log('开始查找用户数据:', phone, name);

    const response = await fetch(`${API_BASE_URL}/api/users/${phone}/${name}`);

    if (response.ok) {
      const result = await response.json();

      if (result.success) {
        console.log('找到用户数据:', result.data);

        // 跳转到报告展示页面
        console.log('跳转到报告展示页面');
        window.location.href = `display.html?phone=${encodeURIComponent(phone)}&name=${encodeURIComponent(name)}`;
      } else {
        console.warn('API返回错误:', result.message);
        errorDiv.innerText = result.message || "未找到匹配的用户数据";
      }
    } else {
      if (response.status === 404) {
        console.warn('未找到匹配的用户数据');
        errorDiv.innerText = "未找到匹配的用户数据";
      } else {
        console.error('API请求失败:', response.status, response.statusText);
        errorDiv.innerText = `查询失败: ${response.status} ${response.statusText}`;
      }
    }
  } catch (error) {
    console.error('查询用户数据失败:', error);
    errorDiv.innerText = `查询失败: ${error.message}`;
  }
}

// 添加回车键监听
document.addEventListener('DOMContentLoaded', function() {
  const phoneInput = document.getElementById('phone');
  const nameInput = document.getElementById('name');

  // 从URL获取参数并自动填充
  const urlParams = new URLSearchParams(window.location.search);
  const phone = urlParams.get('phone');
  const name = urlParams.get('name');

  if (phone) phoneInput.value = phone;
  if (name) nameInput.value = name;

  // 如果两个参数都有，自动查询
  if (phone && name) {
    searchReport();
  }

  // 添加回车键监听
  nameInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      searchReport();
    }
  });

  phoneInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      if (nameInput.value.trim()) {
        searchReport();
      } else {
        nameInput.focus();
      }
    }
  });
});
