#!/bin/bash

# 脚本的用途: 启动健康检测系统的Node.js后端服务

# 获取脚本所在的绝对路径
# SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
# echo "脚本位于: $SCRIPT_DIR"
# cd "$SCRIPT_DIR"
# echo "当前工作目录已切换到: $(pwd)"

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null
then
    echo "错误: Node.js 未安装或未在PATH中找到。"
    echo "请先安装 Node.js 然后再运行此脚本。"
    exit 1
fi

# 检查 server.js 是否存在
if [ ! -f "server.js" ]; then
    echo "错误: server.js 文件未在当前目录找到。"
    echo "请确保您在项目的根目录下运行此脚本，或者 server.js 路径正确。"
    exit 1
fi

echo "正在启动健康检测系统后端服务..."
echo "API将在 http://localhost:3005 (或配置的端口) 上可用"
echo "按 CTRL+C 停止服务."

# 启动 Node.js 服务
# 您可以使用 pm2 或 nodemon 等工具来更好地管理Node.js进程，实现自动重启等功能
# 例如:
# npm install -g pm2
# pm2 start server.js --name health-system-backend
# pm2 logs health-system-backend
#
# 或者使用 nodemon (通常用于开发):
# npm install -g nodemon
# nodemon server.js
#
# 这里使用基础的 node 命令启动:
node server.js

# 脚本执行完毕的退出码
exit $? 