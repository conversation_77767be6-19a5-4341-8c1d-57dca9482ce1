/**
 * 初始化AI修复次数选择框
 */
function initAIFixTimes() {
  const aiRepairSelect = document.getElementById('ai_fix_times');

  // 清空现有选项
  aiRepairSelect.innerHTML = '';

  // 定义AI调频次数选项
  const aiFixOptions = [1, 2, 3, 5, 7, 9, 15, 20, 25, 30];

  // 添加新选项
  aiFixOptions.forEach((value, index) => {
    const option = document.createElement('option');
    option.value = value;
    option.text = `${value}次`;
    if (index === 0) { // 默认选择第一个选项（1次）
      option.selected = true;
    }
    aiRepairSelect.appendChild(option);
  });

  console.log('AI调频次数选择框初始化完成');
}

/**
 * 显示表单验证错误
 * @param {string} message - 错误信息
 * @param {string} fieldId - 字段ID
 */
function showValidationError(message, fieldId) {
  const errorDiv = document.getElementById('error-msg');
  errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;

  // 添加抖动动画
  if (fieldId) {
    const field = document.getElementById(fieldId);
    if (field) {
      field.classList.add('shake');
      field.focus();
      setTimeout(() => field.classList.remove('shake'), 500);
    }
  }
}

/**
 * 显示加载状态
 * @param {boolean} isLoading - 是否正在加载
 */
function setLoadingState(isLoading) {
  const submitButton = document.querySelector('.form-actions .btn:last-child');
  const backButton = document.querySelector('.form-actions .btn:first-child');

  if (isLoading) {
    submitButton.disabled = true;
    backButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
    document.body.style.cursor = 'wait';
  } else {
    submitButton.disabled = false;
    backButton.disabled = false;
    submitButton.innerHTML = '<i class="fas fa-paper-plane"></i> 提交信息';
    document.body.style.cursor = 'default';
  }
}

/**
 * 显示成功消息
 * @param {string} message - 成功信息
 */
function showSuccessMessage(message) {
  // 创建成功消息元素
  const successMessage = document.createElement('div');
  successMessage.className = 'alert alert-success';
  successMessage.style.display = 'flex';
  successMessage.style.alignItems = 'center';
  successMessage.style.justifyContent = 'center';
  successMessage.style.flexDirection = 'column';
  successMessage.style.padding = '30px';
  successMessage.style.textAlign = 'center';

  successMessage.innerHTML = `
    <div style="font-size: 3rem; color: var(--secondary-color); margin-bottom: 20px;">
      <i class="fas fa-check-circle"></i>
    </div>
    <h3 style="margin-bottom: 15px; color: var(--secondary-color);">提交成功！</h3>
    <p>${message}</p>
    <div class="loading-spinner" style="margin: 20px 0;"></div>
    <p>正在跳转到报告页面...</p>
  `;

  // 替换表单内容
  const formCard = document.querySelector('.form-card');
  formCard.innerHTML = '';
  formCard.appendChild(successMessage);
}

/**
 * 验证表单字段
 * @returns {Object|null} 验证通过返回用户数据对象，否则返回null
 */
function validateForm() {
  // 从表单元素获取值
  const userPhone = document.getElementById('phone').value.trim(); // 用户填写的手机号
  const userName = document.getElementById('name').value.trim();   // 用户填写的姓名
  const birthday = document.getElementById('birthday').value.trim();
  const gender = document.getElementById('gender').value;
  const blood_type = document.getElementById('blood_type').value;
  const test_type = document.getElementById('test_type').value;
  const ai_fix_times = document.getElementById('ai_fix_times').value;
  const ai_report = document.getElementById('ai_report').value;

  // 从URL参数获取值
  const urlParams = new URLSearchParams(window.location.search);
  const device_id = (urlParams.get('device_id') && urlParams.get('device_id').trim() !== '') ? urlParams.get('device_id').trim() : '0'; // Default to '0'
  const session_id = urlParams.get('session_id') || null;   // session_id 可以是可选的
  const owner_phone = urlParams.get('phone'); // 机主手机号 from URL
  const owner_name = urlParams.get('name');   // 机主姓名 from URL
  
  // 验证机主信息 (来自URL, 用于数据库索引)
  if (!owner_phone) {
    showValidationError('URL参数中缺少机主手机号码', null);
    return null;
  }
  if (!/^\d{11}$/.test(owner_phone)) {
    showValidationError('URL参数中的机主手机号码格式不正确 (必须为11位数字)', null);
    return null;
  }
  if (!owner_name) {
    showValidationError('URL参数中缺少机主姓名', null);
    return null;
  }
   // 可根据需要添加对 owner_name 的其他验证，例如非空

  // 验证用户填写的手机号
  if (!userPhone) {
    showValidationError('请输入用户手机号码', 'phone');
    return null;
  }
  if (!/^\d{11}$/.test(userPhone)) {
    showValidationError('请输入正确的11位用户手机号码', 'phone');
    return null;
  }

  // 验证用户填写的姓名
  if (!userName) {
    showValidationError('请输入用户姓名', 'name');
    return null;
  }

  // 验证生日
  if (!birthday) {
    showValidationError('请输入出生日期', 'birthday');
    return null;
  }
  if (!/^\d{8}$/.test(birthday)) {
    showValidationError('请输入正确的8位生日，如20180808', 'birthday');
    return null;
  }

  // 验证性别 (如果需要，则添加)
  if (!gender) {
    showValidationError('请选择性别', 'gender');
    return null;
  }

  // 验证血型 (如果需要，则添加)
  if (!blood_type) {
    showValidationError('请选择血型', 'blood_type');
    return null;
  }

  // 创建用户数据对象
  return {
    device_id, // 来自URL或默认
    session_id, // 来自URL或null
    owner_phone: owner_phone.trim(), // 机主手机号 (来自URL)
    owner_name: owner_name.trim(),   // 机主姓名 (来自URL)
    name: userName,       // 用户填写的姓名 (来自表单)
    phone: userPhone,     // 用户填写的手机号 (来自表单)
    birthday,   // 来自表单
    gender,     // 来自表单
    blood_type, // 来自表单
    test_type,  // 来自表单
    ai_fix_times, // 来自表单
    ai_report,  // 来自表单
    submission_time: new Date().toISOString()
  };
}

/**
 * 提交表单
 */
async function submitForm() {
  // 清除错误信息
  document.getElementById('error-msg').innerHTML = '';

  // 验证表单
  const userData = validateForm();
  if (!userData) {
    return;
  }

  // 显示加载状态
  setLoadingState(true);

  try {
    console.log('提交用户数据:', userData);

    // 尝试使用主要方法保存数据
    let saveResult = await saveToCSV(userData);

    // 如果主要方法失败，尝试使用备用方法
    if (!saveResult.success) {
      console.log('主要保存方法失败，正在尝试备用方法...');

      // 显示警告但继续
      showValidationWarning('主要保存方法失败，正在尝试备用方法...');

      // 尝试使用备用方法
      saveResult = await saveWithFallback(userData);
    }

    if (saveResult.success) {
      // 显示成功消息
      showSuccessMessage('您的信息已成功提交，正在跳转到报告页面...');

      // 延迟跳转，给用户一些时间看到成功消息
      setTimeout(() => {
        // 构建相对路径，确保在任何环境下都能正常工作
        // 报告页面使用机主手机号和姓名进行查询
        const reportUrl = `../report/index.html?phone=${encodeURIComponent(userData.owner_phone)}&name=${encodeURIComponent(userData.owner_name)}`;
        console.log('跳转到报告页面:', reportUrl);
        window.location.href = reportUrl;
      }, 2000);
    } else {
      // 如果所有方法都失败，显示错误信息
      showValidationError(saveResult.message || '保存失败，请稍后重试');
      setLoadingState(false);

      // 添加手动跳转按钮
      addManualRedirectButton(userData);
    }
  } catch (err) {
    console.error('提交表单失败:', err);
    showValidationError('提交失败: ' + (err.message || '未知错误，请稍后重试'));
    setLoadingState(false);

    // 添加手动跳转按钮
    addManualRedirectButton(userData);
  }
}

/**
 * 显示警告信息（不是错误）
 * @param {string} message - 警告信息
 */
function showValidationWarning(message) {
  const errorDiv = document.getElementById('error-msg');
  errorDiv.style.color = 'orange';
  errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
}

/**
 * 添加手动跳转按钮
 * @param {Object} userData - 用户数据
 */
function addManualRedirectButton(userData) {
  const errorDiv = document.getElementById('error-msg');
  const redirectButton = document.createElement('button');
  redirectButton.className = 'btn btn-secondary';
  redirectButton.style.marginTop = '15px';
  redirectButton.innerHTML = '<i class="fas fa-external-link-alt"></i> 手动跳转到报告页面';
  redirectButton.onclick = function() {
    // 报告页面使用机主手机号和姓名进行查询
    const reportUrl = `../report/index.html?phone=${encodeURIComponent(userData.owner_phone)}&name=${encodeURIComponent(userData.owner_name)}`;
    window.location.href = reportUrl;
  };

  errorDiv.appendChild(document.createElement('br'));
  errorDiv.appendChild(redirectButton);
}

/**
 * 备用保存方法
 * @param {Object} userData - 用户数据
 * @returns {Promise<Object>} - 保存结果
 */
async function saveWithFallback(userData) {
  try {
    console.log('使用备用方法保存数据...');

    // 保存到localStorage
    localStorage.setItem('userData_' + userData.phone + '_' + userData.name, JSON.stringify(userData));

    // 创建一个隐藏的表单并提交
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/api/users';
    form.style.display = 'none';

    // 添加所有用户数据字段
    for (const key in userData) {
      if (userData.hasOwnProperty(key)) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = userData[key];
        form.appendChild(input);
      }
    }

    // 添加到文档并提交
    document.body.appendChild(form);

    try {
      console.log('尝试提交表单...');
      form.submit();

      // 等待一段时间，看是否有响应
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 如果没有响应，我们假设提交成功
      console.log('备用方法可能成功');
      return { success: true };
    } catch (formError) {
      console.error('表单提交失败:', formError);

      // 即使表单提交失败，我们也假设数据已保存到localStorage
      return {
        success: true,
        message: '数据已保存到本地存储，但未能同步到服务器。您仍然可以查看报告页面。'
      };
    } finally {
      // 清理表单
      document.body.removeChild(form);
    }
  } catch (error) {
    console.error('备用保存方法失败:', error);
    return { success: false, message: '所有保存方法都失败: ' + error.message };
  }
}

/**
 * 保存用户数据
 * @param {Object} userData - 用户数据
 * @returns {Promise<Object>} - 保存结果
 */
async function saveToCSV(userData) {
  try {
    console.log('开始保存用户数据:', userData);

    // 确保device_id是字符串类型
    if (userData.device_id !== undefined && userData.device_id !== null) {
      userData.device_id = String(userData.device_id);
    }

    // 打印完整的请求数据，用于调试
    console.log('发送到服务器的数据:', JSON.stringify(userData, null, 2));

    // 直接通过API保存用户数据
    try {
      // 使用相对路径，而不是硬编码的localhost URL
      // 这样在任何环境下都能正常工作，包括端口转发
      const apiUrl = '/api/users';
      console.log('使用API URL:', apiUrl);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      });

      console.log('服务器响应状态:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('服务器返回错误响应:', errorText);
        throw new Error(`服务器返回错误: ${response.status} ${response.statusText}. 详情: ${errorText}`);
      }

      const result = await response.json();
      console.log('服务器响应数据:', result);

      if (!result.success) {
        throw new Error(result.message || '保存用户数据失败');
      }

      console.log('用户数据保存成功');
      return { success: true };
    } catch (fetchError) {
      console.error('网络请求失败:', fetchError);

      // 获取当前页面URL信息，用于诊断
      const currentUrl = window.location.href;
      const currentOrigin = window.location.origin;
      const currentPath = window.location.pathname;

      console.log('当前页面URL:', currentUrl);
      console.log('当前页面Origin:', currentOrigin);
      console.log('当前页面Path:', currentPath);

      // 尝试使用备用方法保存数据
      console.log('尝试使用备用方法保存数据...');

      // 保存到localStorage
      localStorage.setItem('pendingUserData', JSON.stringify(userData));
      console.log('用户数据已临时保存到localStorage');

      // 尝试使用完整URL路径
      try {
        console.log('尝试使用完整URL路径...');
        const fullApiUrl = `${window.location.origin}/api/users`;
        console.log('完整API URL:', fullApiUrl);

        const fullResponse = await fetch(fullApiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(userData)
        });

        console.log('完整URL请求响应状态:', fullResponse.status, fullResponse.statusText);

        if (fullResponse.ok) {
          const fullResult = await fullResponse.json();
          console.log('完整URL请求响应数据:', fullResult);

          if (fullResult.success) {
            console.log('使用完整URL路径保存成功');
            return { success: true };
          }
        }
      } catch (fullUrlError) {
        console.error('使用完整URL路径请求失败:', fullUrlError);
      }

      return {
        success: false,
        message: `网络请求失败: ${fetchError.message}. 数据已临时保存，请稍后重试。

诊断信息:
- 当前页面: ${currentUrl}
- 请求路径: /api/users
- 错误类型: ${fetchError.name}

可能的解决方法:
1. 确保服务器正在运行
2. 检查网络连接
3. 如果使用端口转发，请确保转发配置正确`
      };
    }
  } catch (error) {
    console.error('保存用户数据失败:', error);
    return { success: false, message: error.message };
  }
}

// 初始化页面
document.addEventListener('DOMContentLoaded', () => {
  console.log('Form page DOM fully loaded and parsed');
  initAIFixTimes();

  // 从URL参数获取信息
  const urlParams = new URLSearchParams(window.location.search);
  const urlDeviceId = urlParams.get('device_id');
  const urlOwnerPhone = urlParams.get('phone'); // 机主手机号
  const urlOwnerName = urlParams.get('name');   // 机主姓名
  // session_id 通常不直接显示在表单中，但如果需要，可以类似处理

  // 注意：不再使用URL中的phone参数预填写表单的'phone'字段，
  // 因为表单中的'phone'字段现在用于用户自己输入手机号。
  // 机主手机号 (urlOwnerPhone) 和机主姓名 (urlOwnerName) 会被包含在提交的数据中。

  if (urlOwnerPhone) {
    console.log(`机主手机号已从URL获取: ${urlOwnerPhone}`);
  }
  if (urlOwnerName) {
    console.log(`机主姓名已从URL获取: ${urlOwnerName}`);
  }

  // device_id 通常不显示在表单让用户修改，它是从URL隐式传递的
  if (urlDeviceId) {
    console.log(`设备ID已从URL获取: ${urlDeviceId}`);
    // 如果有一个隐藏的input来存储device_id，可以在这里设置它的值
    // e.g., document.getElementById('hidden_device_id').value = urlDeviceId;
  }
  
  console.log('Form initialization complete.');
});
