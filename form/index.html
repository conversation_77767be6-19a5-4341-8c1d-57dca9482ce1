<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>信息填写 - 健康评估系统</title>
  <link rel="stylesheet" href="../assets/css/main.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    .form-card {
      max-width: 600px;
      margin: 0 auto;
    }

    .form-header {
      text-align: center;
      margin-bottom: 30px;
    }
    
    .form-header img.logo {
      max-width: 250px;
      display: block;
      margin: 0 auto 20px auto;
    }

    .form-header-text-content {
        text-align: center;
    }

    .form-header h1 {
      color: var(--primary-color);
      margin-bottom: 10px;
    }

    .form-header p {
      color: var(--light-text);
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
    }

    .form-input,
    .form-select {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 15px;
      transition: var(--transition);
    }

    .form-input:focus,
    .form-select:focus {
      border-color: var(--primary-color);
      outline: none;
      box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
    }

    .form-actions {
      display: flex;
      justify-content: space-between;
      margin-top: 25px;
    }

    .required-field::after {
      content: '*';
      color: var(--danger-color);
      margin-left: 4px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="card form-card">
      <div class="form-header">
        <img src="../assets/images/logo_new.jpg" alt="系统Logo" class="logo">
        <div class="form-header-text-content"> 
            <h1>用户信息填写</h1>
            <p>请填写以下信息以开始健康评估</p>
        </div>
      </div>

      <div class="form-group">
        <label for="phone" class="form-label required-field">手机号码</label>
        <input type="tel" id="phone" class="form-input" placeholder="填写11位手机号码" pattern="^\d{11}$" required>
      </div>

      <div class="form-group">
        <label for="name" class="form-label required-field">姓名</label>
        <input type="text" id="name" class="form-input" placeholder="填写姓名" required>
      </div>
      
      <div class="form-group">
        <label for="birthday" class="form-label required-field">出生日期</label>
        <input type="tel" id="birthday" class="form-input" placeholder="填写8位生日，如20180808" pattern="^\d{8}$" required>
      </div>

      <div class="form-group">
        <label for="gender" class="form-label">性别</label>
        <select id="gender" class="form-select">
          <option value="">请选择性别</option>
          <option value="男">男</option>
          <option value="女">女</option>
        </select>
      </div>

      <div class="form-group">
        <label for="blood_type" class="form-label">血型</label>
        <select id="blood_type" class="form-select">
          <option value="">请选择血型</option>
          <option value="A">A型</option>
          <option value="B">B型</option>
          <option value="AB">AB型</option>
          <option value="O">O型</option>
          <option value="未知">未知</option>
        </select>
      </div>

      <div class="form-group">
        <label for="test_type" class="form-label">检测类型</label>
        <select id="test_type" class="form-select">
          <option value="标准" selected>标准</option>
          <option value="快速">快速</option>
          <option value="详细">详细</option>
        </select>
      </div>

      <div class="form-group">
        <label for="ai_fix_times" class="form-label">AI调频次数</label>
        <select id="ai_fix_times" class="form-select"></select>
      </div>

      <div class="form-group">
        <div style="background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin-bottom: 15px; font-size: 0.9rem; line-height: 1.6;">
          <h6 style="margin: 0 0 10px 0; color: var(--primary-color); font-weight: 600;"><i class="fas fa-info-circle" style="margin-right: 5px;"></i>信息收集声明</h6>
          <p style="margin: 0; color: #6c757d;">我们承诺严格保护您的个人信息安全，所收集的信息仅用于健康评估分析，不会用于其他商业用途。您的数据将采用加密存储，并严格按照相关法律法规进行处理。</p>
        </div>
      </div>

      <div class="form-group">
        <label for="ai_report" class="form-label">AI报告</label>
        <select id="ai_report" class="form-select">
          <option value="否">否</option>
          <option value="是">是</option>
        </select>
      </div>

      <div id="error-msg" style="color: var(--danger-color); margin-bottom: 20px;"></div>

      <div class="form-actions">
        <button class="btn btn-outline" onclick="window.location.href='../index.html'">
          <i class="fas fa-arrow-left"></i> 返回首页
        </button>
        <button class="btn" onclick="submitForm()">
          <i class="fas fa-paper-plane"></i> 提交信息
        </button>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="footer">
    <div class="footer-content">
      <p class="footer-text">© 2025 健康评估系统由上海康德怡科技版权提供，并结合deepseek大模型AI驱动的智能报告解读系统，提供个性化的健康解决方案，提升生活质量</p>
      <p class="footer-text">提供全面的健康检测解决方案</p>
    </div>
  </footer>

  <script src="../assets/js/main.js"></script>
  <script src="form.js"></script>
</body>
</html>
