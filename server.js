// Import necessary modules
const express = require('express');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');
const cors = require('cors');
const util = require('util');
const dbService = require('./utils/database-service'); // Import database service
const adminService = require('./utils/admin-service'); // Import admin service
const https = require('https'); // Add https module
const qr = require('qrcode'); // Re-add qrcode library

// Convert fs's asynchronous operations to Promise
const readFileAsync = util.promisify(fs.readFile);
const writeFileAsync = util.promisify(fs.writeFile);
const statAsync = util.promisify(fs.stat);
const mkdirAsync = util.promisify(fs.mkdir);
const existsAsync = util.promisify(fs.exists);

// Create Express application
const app = express();
const PORT = 3005;

// Configure middleware
// 允许所有来源的跨域请求
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// 添加额外的CORS处理中间件，确保在任何环境下都能正常工作
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }

  next();
});

app.use(bodyParser.json({ limit: '1mb' }));
app.use(bodyParser.urlencoded({ extended: true })); // 支持表单提交

app.use(express.static('.'));

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${req.method} ${req.originalUrl} ${res.statusCode} ${duration}ms`);
  });
  next();
});

// In-memory cache
const cache = {
  users: null,  // User list cache
  userInfo: {}, // User info cache
  reports: {},  // Report data cache
  lastUpdated: {
    users: 0,   // User list last updated time
    userInfo: {}, // User info last updated time
    reports: {}   // Report data last updated time
  },
  cacheTime: 5 * 60 * 1000 // Cache validity period: 5 minutes
};

// 确保数据目录存在
const dataDir = path.join(__dirname, 'data');
const usersDir = path.join(dataDir, 'users');

if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir);
  console.log(`Created data directory: ${dataDir}`);
}

if (!fs.existsSync(usersDir)) {
  fs.mkdirSync(usersDir);
  console.log(`Created users directory: ${usersDir}`);
}

// 优雅关闭函数
function gracefulShutdown() {
  console.log('Shutting down server...');

  // 关闭数据库连接
  dbService.closeDatabaseConnections()
    .catch(err => console.error('Failed to close database connections:', err))
    .finally(() => {
      // 退出进程
      process.exit(0);
    });
}

// 监听进程终止信号
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// 启动服务器
app.listen(PORT, async () => {
  console.log(`Server started at http://localhost:${PORT}`);

  // 初始化数据库服务
  try {
    await dbService.initDatabaseService();
    console.log('Database service initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database service:', error);
  }
});

// 获取用户数据
app.get('/api/users/:phone/:name', async (req, res) => {
  try {
    const { phone, name } = req.params;
    console.log(`Searching for user: ${name} with phone: ${phone}`);

    // 1. 先从数据库查询
    const userId = await dbService.getUserIdByPhoneAndName(phone, name);
    if (userId) {
      console.log(`Found user in database with ID: ${userId}`);

      // 获取用户信息
      const userInfo = await getUserInfoFromDatabase(userId);

      // 获取用户报告
      const reportData = await dbService.getLatestReportByUserId(userId);

      return res.json({
        success: true,
        data: {
          userInfo,
          reportData
        }
      });
    }

    // 2. 从文件系统查询
    const userFolderName = `${phone}_${name}`;
    const userFolderPath = path.join(usersDir, userFolderName);

    if (await existsAsync(userFolderPath)) {
      console.log(`Found user folder: ${userFolderPath}`);

      // 读取用户信息
      const userInfoPath = path.join(userFolderPath, 'user_info.json');
      let userInfo = null;

      if (await existsAsync(userInfoPath)) {
        const userInfoData = await readFileAsync(userInfoPath, 'utf8');
        userInfo = JSON.parse(userInfoData);
      }

      // 读取报告数据
      const reportPath = path.join(userFolderPath, 'report_data.json');
      let reportData = null;

      if (await existsAsync(reportPath)) {
        const reportDataText = await readFileAsync(reportPath, 'utf8');
        reportData = JSON.parse(reportDataText);
      }

      return res.json({
        success: true,
        data: {
          userInfo,
          reportData
        }
      });
    }

    // 3. 查询users.csv
    const usersFilePath = path.join(dataDir, 'users.csv');
    if (await existsAsync(usersFilePath)) {
      const csvData = await readFileAsync(usersFilePath, 'utf8');
      const lines = csvData.split('\n');

      if (lines.length > 1) {
        const headers = lines[0].split(',');

        for (let i = 1; i < lines.length; i++) {
          const values = lines[i].split(',');
          if (values.length === headers.length) {
            const userData = {};

            for (let j = 0; j < headers.length; j++) {
              userData[headers[j]] = values[j];
            }

            if (userData.phone === phone && userData.name === name) {
              console.log(`Found user in CSV: ${name}`);
              return res.json({
                success: true,
                data: {
                  userInfo: userData,
                  reportData: null
                }
              });
            }
          }
        }
      }
    }

    console.log(`User not found: ${name} with phone: ${phone}`);
    return res.status(404).json({
      success: false,
      message: "User not found"
    });
  } catch (error) {
    console.error('Error fetching user data:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch user data' });
  }
});

// 辅助函数：从数据库获取用户信息
async function getUserInfoFromDatabase(userId) {
  // 从SQLite或SQL Server获取用户详细信息
  try {
    // 尝试直接从SQLite获取用户信息
    if (dbService.getSqliteDb) {
      const db = dbService.getSqliteDb();
      if (db) {
        return new Promise((resolve, reject) => {
          db.get(
            'SELECT * FROM users WHERE id = ?',
            [userId],
            (err, row) => {
              if (err) reject(err);
              else resolve(row);
            }
          );
        });
      }
    }

    // 如果没有方法获取数据库实例，则返回null
    return null;
  } catch (error) {
    console.error('Error getting user info from database:', error);
    return null;
  }
}

// 保存用户数据
app.post('/api/users', async (req, res) => {
  try {
    const userData = req.body;

    if (!userData || !userData.name || !userData.phone) {
      return res.status(400).json({ success: false, message: 'Missing required user information' });
    }

    // 打印完整的用户数据，用于调试
    console.log('Received user data:', JSON.stringify(userData, null, 2));

    // 确保device_id是字符串类型，并设置默认值
    if (userData.device_id !== undefined && userData.device_id !== null) {
      userData.device_id = String(userData.device_id);
    } else {
      userData.device_id = '0';
    }
    // 为 session_id 设置默认值
    if (userData.session_id !== undefined && userData.session_id !== null && userData.session_id !== '') {
      userData.session_id = String(userData.session_id);
    } else {
      userData.session_id = '0';
    }

    console.log('Saving user data:', userData.name, userData.phone, 'Device ID:', userData.device_id, 'Session ID:', userData.session_id);

    // 1. 保存到文件系统
    try {
      // 创建用户文件夹
      const userFolderName = `${userData.phone}_${userData.name}`;
      const userFolderPath = path.join(usersDir, userFolderName);

      if (!fs.existsSync(userFolderPath)) {
        await mkdirAsync(userFolderPath);
      }

      // 保存用户信息JSON文件
      const userInfoPath = path.join(userFolderPath, 'user_info.json');
      await writeFileAsync(userInfoPath, JSON.stringify(userData, null, 2), 'utf8');

      // 更新users.csv文件
      const usersFilePath = path.join(dataDir, 'users.csv');
      
      // 定义期望的CSV头部顺序，确保包含新字段
      // 从 userData 获取基本键，并确保 device_id 和 session_id 存在
      // 注意：数据库中的id, created_at, updated_at 不一定在 userData 中，而 submission_time 在
      const allKeys = [
        'name', 'phone', 'birthday', 'gender', 'blood_type', 
        // 'height', 'weight', // Removed as per request
        'device_id', 'session_id', 
        'test_type', 'ai_fix_times', 'ai_report', 'submission_time'
      ];
      // 确保 userData 中存在的其他字段也被考虑，或者固定一个明确的列表
      // 这里我们基于 allKeys，如果 userData 中有额外字段，它们不会被写入 CSV
      // 如果 userData 中缺少 allKeys 中的字段，会写入空字符串

      let newDataRows = [];
      let headerWritten = false;

      if (await existsAsync(usersFilePath)) {
        const existingCsv = await readFileAsync(usersFilePath, 'utf8');
        const lines = existingCsv.split('\n').filter(line => line.trim() !== '');

        if (lines.length > 0) {
          const currentCsvHeaders = lines[0].split(',').map(h => h.trim());
          newDataRows.push(allKeys.join(',')); // 使用新的标准头部
          headerWritten = true;

          let userFoundAndUpdated = false;
          for (let i = 1; i < lines.length; i++) {
            const values = lines[i].split(',');
            const oldLineData = {};
            currentCsvHeaders.forEach((header, index) => {
              oldLineData[header] = values[index] ? values[index].trim() : '';
            });

            if (oldLineData.phone === userData.phone && oldLineData.name === userData.name) {
              // 这是要更新的用户，使用新数据，并按新表头顺序排列
              const updatedValues = allKeys.map(key => userData[key] || '');
              newDataRows.push(updatedValues.join(','));
              userFoundAndUpdated = true;
            } else {
              // 这是其他用户，按新表头顺序重新映射旧数据
              const remappedValues = allKeys.map(key => oldLineData[key] || '');
              newDataRows.push(remappedValues.join(','));
            }
          }

          if (!userFoundAndUpdated) {
            // 新用户，添加到末尾
            const newValues = allKeys.map(key => userData[key] || '');
            newDataRows.push(newValues.join(','));
          }
        }
      }

      if (!headerWritten) { // 文件不存在，或者存在但为空
        newDataRows.push(allKeys.join(',')); // 写入表头
        const newValues = allKeys.map(key => userData[key] || '');
        newDataRows.push(newValues.join(',')); // 写入新用户数据
      }
      
      // 确保最后一行后也有换行符，并且文件内容以换行符结尾
      let csvContentToWrite = newDataRows.join('\n');
      if (!csvContentToWrite.endsWith('\n')) {
        csvContentToWrite += '\n';
      }

      await writeFileAsync(usersFilePath, csvContentToWrite, 'utf8');
      console.log('User data saved to filesystem (users.csv) successfully');
    } catch (error) {
      console.error('Error saving user data to filesystem:', error);
      // 继续尝试保存到数据库
    }

    // 2. 同步到数据库
    try {
      const dbResult = await dbService.syncUserToDatabase(userData);
      console.log(`User data synced to database. User ID: ${dbResult.userId}, Is new user: ${dbResult.isNewUser}`);
    } catch (error) {
      console.error('Error syncing user data to database:', error);
      // 如果文件系统保存也失败，则返回错误
      if (!fs.existsSync(path.join(usersDir, `${userData.phone}_${userData.name}`))) {
        return res.status(500).json({ success: false, message: 'Failed to save user data' });
      }
    }

    return res.json({ success: true, message: 'User data saved successfully' });
  } catch (error) {
    console.error('Failed to save user data:', error);
    res.status(500).json({ success: false, message: 'Failed to save user data' });
  }
});

// 获取用户报告数据
app.get('/api/reports/:phone/:name', async (req, res) => {
  try {
    const { phone, name } = req.params;
    console.log(`Retrieving report for user: ${name} with phone: ${phone}`);

    // 1. 先获取用户ID
    const userId = await dbService.getUserIdByPhoneAndName(phone, name);
    if (userId) {
      console.log(`Found user in database with ID: ${userId}`);

      // 获取用户报告
      const reportData = await dbService.getLatestReportByUserId(userId);

      if (reportData) {
        return res.json({
          success: true,
          data: reportData
        });
      } else {
        console.log(`No report found for user: ${name} with phone: ${phone}`);
      }
    }

    // 2. 尝试从文件系统获取报告
    const userFolderName = `${phone}_${name}`;
    const userFolderPath = path.join(usersDir, userFolderName);

    if (await existsAsync(userFolderPath)) {
      console.log(`Checking report in user folder: ${userFolderPath}`);

      // 读取报告数据
      const reportPath = path.join(userFolderPath, 'report_data.json');

      if (await existsAsync(reportPath)) {
        const reportDataText = await readFileAsync(reportPath, 'utf8');
        const reportData = JSON.parse(reportDataText);

        return res.json({
          success: true,
          data: reportData
        });
      }
    }

    // 不再自动生成默认报告，而是返回404错误
    return res.status(404).json({
      success: false,
      message: "找不到体检报告数据\n体检报告数据只能从服务器同步获取，不会在创建用户时自动生成。\n\n可能的原因：\n\n您的体检报告尚未生成\n服务器无法获取您的用户数据，无法生成体检报告\n您输入的信息有误\n服务器会每30分钟自动尝试同步一次所有用户的体检数据，但只有在成功获取到用户数据时才会生成体检报告。\n\n如果您确认已正确填写用户信息，请稍后再查询。\n\n 提示：只有在用户信息完整的情况下，系统才会生成体检报告。"
    });
  } catch (error) {
    console.error('Error fetching report data:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch report data' });
  }
});

// 数据库管理API端点

// 获取数据库统计信息
app.get('/api/admin/stats', async (req, res) => {
  try {
    const stats = await adminService.getDatabaseStats();
    res.json({
      success: true,
      ...stats
    });
  } catch (error) {
    console.error('Error getting database stats:', error);
    res.status(500).json({ success: false, message: 'Failed to get database statistics' });
  }
});

// 获取所有用户
app.get('/api/admin/users', async (req, res) => {
  try {
    const users = await adminService.getAllUsers();
    res.json({
      success: true,
      users
    });
  } catch (error) {
    console.error('Error getting users:', error);
    res.status(500).json({ success: false, message: 'Failed to get users' });
  }
});

// 获取用户详情
app.get('/api/admin/users/:id', async (req, res) => {
  try {
    const user = await adminService.getUserById(parseInt(req.params.id));
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }
    res.json({
      success: true,
      user
    });
  } catch (error) {
    console.error(`Error getting user ${req.params.id}:`, error);
    res.status(500).json({ success: false, message: 'Failed to get user details' });
  }
});

// 删除用户
app.delete('/api/admin/users/:id', async (req, res) => {
  try {
    const result = await adminService.deleteUser(parseInt(req.params.id));
    if (!result) {
      return res.status(404).json({ success: false, message: 'User not found or could not be deleted' });
    }
    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting user ${req.params.id}:`, error);
    res.status(500).json({ success: false, message: 'Failed to delete user' });
  }
});

// 获取所有报告
app.get('/api/admin/reports', async (req, res) => {
  try {
    const reports = await adminService.getAllReports();
    res.json({
      success: true,
      reports
    });
  } catch (error) {
    console.error('Error getting reports:', error);
    res.status(500).json({ success: false, message: 'Failed to get reports' });
  }
});

// 获取报告详情
app.get('/api/admin/reports/:id', async (req, res) => {
  try {
    const report = await adminService.getReportById(parseInt(req.params.id));
    if (!report) {
      return res.status(404).json({ success: false, message: 'Report not found' });
    }
    res.json({
      success: true,
      report
    });
  } catch (error) {
    console.error(`Error getting report ${req.params.id}:`, error);
    res.status(500).json({ success: false, message: 'Failed to get report details' });
  }
});

// 删除报告
app.delete('/api/admin/reports/:id', async (req, res) => {
  try {
    const result = await adminService.deleteReport(parseInt(req.params.id));
    if (!result) {
      return res.status(404).json({ success: false, message: 'Report not found or could not be deleted' });
    }
    res.json({
      success: true,
      message: 'Report deleted successfully'
    });
  } catch (error) {
    console.error(`Error deleting report ${req.params.id}:`, error);
    res.status(500).json({ success: false, message: 'Failed to delete report' });
  }
});

// 备份数据库
app.post('/api/admin/backup', async (req, res) => {
  try {
    const result = await adminService.backupDatabase();
    res.json(result);
  } catch (error) {
    console.error('Error backing up database:', error);
    res.status(500).json({ success: false, message: 'Failed to backup database' });
  }
});

// 同步文件系统和数据库
app.post('/api/admin/sync', async (req, res) => {
  try {
    const result = await adminService.syncFileSystemAndDatabase();
    res.json(result);
  } catch (error) {
    console.error('Error syncing file system and database:', error);
    res.status(500).json({ success: false, message: 'Failed to sync file system and database' });
  }
});

/**
 * 根据生日计算年龄
 * @param {string} birthday - 格式为YYYYMMDD的生日字符串
 * @returns {number} 年龄
 */
function calculateAge(birthday) {
  if (!birthday || birthday.length !== 8) {
    return 0;
  }

  const year = parseInt(birthday.substring(0, 4));
  const month = parseInt(birthday.substring(4, 6));
  const day = parseInt(birthday.substring(6, 8));

  const birthDate = new Date(year, month - 1, day);
  const today = new Date();

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  return age;
}

// 注意：我们不再提供手动同步API
// 体检报告数据只能通过实际的外部API自动同步

// 新增API：获取并返回二维码图片
app.get('/api/qrcode-image', async (req, res) => {
  const imageUrl = req.query.url;

  if (!imageUrl) {
    return res.status(400).json({ success: false, message: 'Missing image URL' });
  }

  try {
    // 确保我们只处理 HTTPS URL，或者根据需要添加 HTTP 支持
    if (!imageUrl.startsWith('https://')) {
      // 对于 http，需要使用 http.get
      // 为了简单起见，这里仅演示 https
      // 如果需要同时支持 http 和 https，可以根据 URL 协议选择模块
      if (imageUrl.startsWith('http://')) {
        const http = require('http');
        http.get(imageUrl, (imageRes) => {
          if (imageRes.statusCode !== 200) {
            res.status(imageRes.statusCode || 500).json({ success: false, message: 'Failed to fetch image' });
            return;
          }
          // 从原始响应中获取 Content-Type
          const contentType = imageRes.headers['content-type'];
          if (contentType) {
            res.setHeader('Content-Type', contentType);
          }
          imageRes.pipe(res); // 将图片流直接pipe到响应
        }).on('error', (err) => {
          console.error('Error fetching image via http:', err);
          res.status(500).json({ success: false, message: 'Failed to fetch image' });
        });
      } else {
        return res.status(400).json({ success: false, message: 'Invalid image URL protocol. Only http and https are supported.' });
      }
      return; // 返回以避免执行下面的 https.get
    }
    
    // 使用 https 模块获取图片
    https.get(imageUrl, (imageRes) => {
      // 检查状态码
      if (imageRes.statusCode !== 200) {
        // 将原始错误状态码和消息传递回去，如果可用
        res.status(imageRes.statusCode || 500).json({ success: false, message: 'Failed to fetch image' });
        return;
      }

      // 从原始响应中获取 Content-Type
      const contentType = imageRes.headers['content-type'];
      if (contentType) {
        res.setHeader('Content-Type', contentType);
      }
      // 将图片流直接pipe到响应，这样可以高效处理大文件，无需完全加载到内存
      imageRes.pipe(res);

    }).on('error', (err) => {
      console.error('Error fetching image via https:', err);
      res.status(500).json({ success: false, message: 'Failed to fetch image' });
    });

  } catch (error) {
    console.error('Error in /api/qrcode-image endpoint:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
});

// API端点：根据参数生成并直接返回二维码图片
app.get('/api/generate-qr-image', async (req, res) => {
  try {
    let { device_id, phone, session_id } = req.query;

    // 基本参数校验和默认值设置
    if (!device_id || device_id.trim() === '') {
      // return res.status(400).send('错误：缺少 device_id 参数。');
      device_id = '0'; // Default to '0' if missing or empty
    }
    // 手机号和 session_id 可以是可选的，具体取决于需求
    // if (!phone) {
    //   return res.status(400).send('错误：缺少 phone 参数。');
    // }

    // 构建要编码到二维码中的URL
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    let formUrl = `${baseUrl}/form/index.html?device_id=${encodeURIComponent(device_id)}`;
    if (phone) {
      formUrl += `&phone=${encodeURIComponent(phone)}`;
    }
    if (session_id) {
      formUrl += `&session_id=${encodeURIComponent(session_id)}`;
    }

    console.log(`服务器端为API /api/generate-qr-image 生成二维码，内容: ${formUrl}`);

    // 生成二维码图片buffer
    const options = {
      type: 'png',
      errorCorrectionLevel: 'L', // 容错级别
      scale: 10, // 图片大小（模块像素数）
      margin: 2    // 二维码边距
    };
    const qrImageBuffer = await qr.toBuffer(formUrl, options);

    // 发送图片作为响应
    res.setHeader('Content-Type', 'image/png');
    res.send(qrImageBuffer);

  } catch (error) {
    console.error('服务器端 /api/generate-qr-image 生成二维码失败:', error);
    res.status(500).send('生成二维码图片时发生错误。');
  }
});
