# 健康检测系统

这是一个健康检测系统，包含三个主要功能：二维码生成、信息填写和报告查询。系统支持文件系统和SQL数据库双重存储模式。

## 文件夹结构

```
wechat_dev/
├── assets/                  # 静态资源文件夹
│   ├── css/                 # CSS样式文件
│   ├── js/                  # JavaScript文件
│   └── images/              # 图片资源
├── data/                    # 数据存储文件夹
│   ├── health_check.db      # SQLite数据库文件
│   ├── users.csv            # 用户信息CSV文件
│   └── users/               # 用户文件夹
│       └── {phone}_{name}/  # 每个用户的文件夹
│           ├── user_info.json    # 用户信息JSON文件
│           └── report_data.json  # 用户体检数据JSON文件
├── qrcode/                  # 二维码生成相关
│   ├── index.html           # 二维码生成页面
│   └── qrcode.js            # 二维码生成逻辑
├── form/                    # 信息填写相关
│   ├── index.html           # 信息填写页面
│   └── form.js              # 表单处理逻辑
├── report/                  # 报告查询相关
│   ├── index.html           # 报告查询页面
│   ├── display.html         # 报告展示页面
│   └── report.js            # 报告处理逻辑
├── utils/                   # 工具函数
│   ├── csv-handler.js       # CSV文件处理
│   ├── json-handler.js      # JSON数据处理
│   ├── sync-service.js      # 数据同步服务
│   └── database-service.js  # 数据库服务
├── index.html               # 主页面
├── server.js                # Node.js服务器
├── package.json             # 项目依赖
├── start.bat                # 一体化启动脚本（安装依赖、初始化数据库、启动服务器）
└── README.md                # 说明文档
```

## 使用方法

### 快速启动（Windows环境）

系统提供了一体化的启动脚本，只需：

1. 双击`start.bat`文件
2. 脚本会自动：
   - 检查和安装依赖
   - 初始化数据库
   - 启动服务器
   - 在浏览器中打开应用

这一步操作将完成所有必要的设置，并启动系统。

### 手动启动（其他环境）

如果`start.bat`无法正常工作，可以手动完成以下步骤：

```bash
# 安装依赖
npm install

# 启动服务器
node server.js
```

然后在浏览器中访问 http://localhost:3005

**注意：** 请保持服务器窗口打开，不要关闭，否则系统将无法正常工作。

### 使用系统

系统包含三个主要功能：

1. **生成二维码**
   - 输入设备ID，生成包含该ID的二维码
   - 用户扫描二维码后，可以跳转到信息填写页面

2. **信息填写**
   - 用户填写个人信息（姓名、电话、生日、性别、血型等）
   - 提交后，系统会创建用户文件夹、保存用户信息JSON文件并同步到数据库

3. **查询报告**
   - 输入手机号和姓名，查询对应的检测报告
   - 系统会自动创建报告数据文件（如果不存在）并同步到数据库
   - 显示用户的检测报告

## 技术说明

1. **前端**
   - 使用原生HTML、CSS和JavaScript
   - 不依赖任何前端框架

2. **后端**
   - 使用Node.js和Express框架
   - 同时支持文件系统和数据库存储

3. **数据存储**
   - 文件系统存储
     - 用户列表存储在CSV文件中
     - 用户详细信息和报告数据存储在JSON文件中
   - 数据库存储
     - 默认使用SQLite本地数据库
     - 可选配置SQL Server

## 数据库配置

系统支持两种数据库：

1. **SQLite** (默认)
   - 无需额外配置，自动创建和使用
   - 数据库文件位置：`data/health_check.db`

2. **SQL Server**
   - 需要在 `utils/database-service.js` 中启用并配置连接信息
   - 配置示例:
   ```js
   const DB_CONFIG = {
     sqlserver: {
       enabled: true, // 将此项设置为true以启用SQL Server
       config: {
         user: 'sa',               // 用户名
         password: 'YourPassword', // 密码
         server: 'localhost',      // 服务器地址
         database: 'HealthCheckDB', // 数据库名称
         options: {
           encrypt: false,
           trustServerCertificate: true
         }
       }
     }
   };
   ```

## Windows环境特别说明

1. 一体化的start.bat脚本已针对Windows环境优化
2. 所有控制台输出使用英文，避免中文显示问题
3. 如果在Windows下遇到权限问题，请尝试以管理员身份运行start.bat

## 注意事项

1. 系统需要Node.js环境才能运行
2. 如果使用SQL Server，需要预先创建数据库
3. 所有数据默认存储在本地，文件系统和数据库同步存储
4. 报告数据是自动生成的模拟数据，不代表真实的健康检测结果

## 数据库管理

系统提供了一个简单的数据库管理界面，您可以通过以下方式访问：

1. 从主页底部点击"数据库管理"链接
2. 直接访问 http://localhost:3005/admin/index.html

### 管理功能

数据库管理界面提供以下功能：

1. **数据库信息**：查看数据库类型、路径、用户数量和报告数量
2. **用户管理**：查看、搜索和删除用户
3. **报告管理**：查看、搜索和删除报告
4. **备份数据库**：创建数据库的备份副本
5. **同步数据**：将文件系统中的数据同步到数据库

### 注意事项

- 删除用户会同时删除该用户的所有报告数据
- 备份文件保存在 `data/backups` 目录下
- 同步操作会扫描文件系统中的用户文件夹，并将数据同步到数据库中
