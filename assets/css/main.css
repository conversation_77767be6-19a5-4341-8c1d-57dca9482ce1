/* 全局样式 */
:root {
  --primary-color: #4285f4;
  --primary-dark: #3367d6;
  --secondary-color: #34a853;
  --accent-color: #fbbc05;
  --danger-color: #ea4335;
  --text-color: #333333;
  --light-text: #757575;
  --background-color: #f8f9fa;
  --card-background: #ffffff;
  --border-color: #e0e0e0;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
  padding: 0;
  margin: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 导航栏 */
.navbar {
  background-color: var(--primary-color);
  color: white;
  padding: 15px 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.navbar-logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  text-decoration: none;
}

.navbar-menu {
  display: flex;
  list-style: none;
}

.navbar-item {
  margin-left: 20px;
}

.navbar-link {
  color: white;
  text-decoration: none;
  padding: 5px 10px;
  border-radius: 4px;
  transition: var(--transition);
}

.navbar-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.navbar-link.active {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 卡片样式 */
.card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 25px;
  margin-bottom: 20px;
  transition: var(--transition);
}

.card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.card-title {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: var(--primary-color);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 16px;
  transition: var(--transition);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 12px 24px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: var(--transition);
  text-align: center;
  text-decoration: none;
}

.btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.btn:active {
  transform: translateY(0);
}

.btn-secondary {
  background-color: var(--secondary-color);
}

.btn-secondary:hover {
  background-color: #2d9348;
}

.btn-danger {
  background-color: var(--danger-color);
}

.btn-danger:hover {
  background-color: #d32f2f;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: white;
}

/* 提示信息样式 */
.alert {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary-color);
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar-menu {
    display: none;
  }
  
  .form-input, 
  .form-select, 
  .form-textarea,
  .btn {
    padding: 10px;
    font-size: 14px;
  }
  
  .card {
    padding: 15px;
  }
  
  .card-title {
    font-size: 1.2rem;
  }

  /* 移动端页脚优化 */
  .footer-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .footer-text {
    font-size: 0.85rem;
    margin-bottom: 12px;
    line-height: 1.6;
  }

  .footer-text:last-child {
    margin-bottom: 0;
  }
}

/* 页脚样式 */
.footer {
  background-color: #333;
  color: white;
  padding: 20px 0;
  margin-top: 40px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-text {
  font-size: 0.9rem;
}

/* 报告页面特定样式 */
.report-container {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 30px;
  margin-top: 20px;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.report-title {
  font-size: 1.8rem;
  color: var(--primary-color);
}

.report-date {
  color: var(--light-text);
  font-size: 0.9rem;
}

.report-section {
  margin-bottom: 25px;
}

.report-section-title {
  font-size: 1.3rem;
  color: var(--secondary-color);
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px dashed var(--border-color);
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.report-table th,
.report-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.report-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.report-table tr:hover {
  background-color: #f9f9f9;
}

.status-normal {
  color: var(--secondary-color);
  font-weight: 500;
}

.status-warning {
  color: var(--accent-color);
  font-weight: 500;
}

.status-danger {
  color: var(--danger-color);
  font-weight: 500;
}

/* 超小屏幕页脚优化 */
@media (max-width: 480px) {
  .footer {
    padding: 15px 0;
  }

  .footer-content {
    padding: 0 15px;
  }

  .footer-text {
    font-size: 0.8rem;
    margin-bottom: 15px;
    line-height: 1.7;
    padding: 0 10px;
  }

  .footer-text:last-child {
    margin-bottom: 0;
  }
}
