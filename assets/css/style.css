/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Helvetica Neue", "PingFang SC", "Microsoft YaHei", sans-serif;
}

body {
  background: #f5f5f5;
  padding: 20px;
  color: #333;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  max-width: 50%;
  height: auto;
  margin: 20px auto;
  display: block;
}

h1, h2, h3 {
  color: #278bf5;
  margin-bottom: 15px;
}

/* 表单样式 */
.form-group {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.label-box {
  width: 80px;
  background-color: #278bf5;
  color: white;
  text-align: center;
  padding: 12px 0;
  border-radius: 8px 0 0 8px;
  font-weight: bold;
  font-size: 14px;
}

input, select {
  flex: 1;
  padding: 12px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-left: none;
  border-radius: 0 8px 8px 0;
  outline: none;
}

input:focus, select:focus {
  border-color: #278bf5;
}

.error-msg {
  color: red;
  margin-top: 8px;
  font-size: 14px;
  height: 18px;
  text-align: left;
}

.button-primary {
  width: 100%;
  background-color: #278bf5;
  color: white;
  font-weight: bold;
  border: none;
  border-radius: 8px;
  padding: 14px;
  margin-top: 20px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.button-primary:hover {
  background-color: #1a6fd0;
}

.button-primary:disabled {
  background-color: #a0c4f0;
  cursor: not-allowed;
}

/* 二维码样式 */
.qrcode-container {
  text-align: center;
  margin: 30px auto;
}

#qrcode {
  margin: 0 auto;
  padding: 15px;
  background: white;
  border-radius: 5px;
  display: inline-block;
}

/* 报告样式 */
.report-container {
  margin-top: 20px;
}

.report-section {
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}

.report-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.report-item:nth-child(odd) {
  background-color: #f9f9f9;
}

.severity-high {
  color: #e74c3c;
  font-weight: bold;
}

.severity-medium {
  color: #f39c12;
  font-weight: bold;
}

.severity-low {
  color: #27ae60;
}

.user-info {
  background-color: #f0f7ff;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.user-info p {
  margin: 5px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: url('data:image/svg+xml;utf8,<svg fill="gray" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>') no-repeat right 10px center;
    background-size: 16px 16px;
    padding-right: 30px;
  }
}
