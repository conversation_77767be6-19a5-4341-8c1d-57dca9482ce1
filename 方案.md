好的，这是一个基于您提供的需求的软件设计总体方案。

**软件设计总体方案**

**1. 引言**

*   **1.1 项目背景:**
    为满足公众号应用需求，开发一套集二维码扫描、用户信息录入、用户数据存储、报告查询与展示，并包含特定业务逻辑（重复项标记、医学资料查询）的系统。
*   **1.2 设计目标:**
    *   构建一个稳定、高效、可扩展、易于维护的软件系统。
    *   确保用户体验良好，界面美观，操作便捷。
    *   满足所有一期及新增需求中定义的功能和性能要求。
    *   实现与甲方现有系统的有效集成（通过API）。
*   **1.3 范围:**
    本方案覆盖了需求文档中定义的所有模块，包括前端H5应用、后端服务API、数据库设计以及部署策略。
*   **1.4 名词解释:**
    *   **甲方:** 需求提出方，提供检测仪、API接口、服务器环境等。
    *   **乙方:** 本软件系统的开发方（个人兼职）。
    *   **H5页面:** 基于HTML5技术的移动端网页。
    *   **API:** 应用程序编程接口。

**2. 系统架构**

*   **2.1 总体架构图 (逻辑分层):**

    ```mermaid
    graph TD
        subgraph "用户端"
            A[微信客户端] -- 扫码 --> B(检测仪二维码)
            B -- 跳转 --> C{"H5应用 (运行于浏览器)"}
        end

        subgraph "乙方系统"
            C -- HTTPS/HTTP --> D["前端静态资源服务器 / API网关 (如Nginx)"]
            D -- API请求 --> E{后端应用服务}
            E -- 读写 --> F["乙方数据库<br>用户信息, 设备记录<br>医学资料"]
            E -- 调用API --> G["甲方检测仪API <br>(写入用户信息)"]
            E -- 调用API --> H["甲方报告API <br>(拉取报告JSON)"]
        end

        subgraph "甲方系统"
            G
            H
        end

        style C fill:#f9f,stroke:#333,stroke-width:2px
        style E fill:#ccf,stroke:#333,stroke-width:2px
        style F fill:#lightgrey,stroke:#333,stroke-width:2px
    ```

*   **2.2 架构说明:**
    *   **用户端:** 用户通过微信扫描二维码，访问部署在服务器上的H5应用。
    *   **前端层 (H5应用):** 负责用户交互、信息收集、数据展示。通过API与后端服务通信。
    *   **后端应用服务层:** 核心业务逻辑处理，包括：
        *   二维码生成API（供甲方检测仪调用）。
        *   用户信息接收、校验、存储，并调用甲方API写入。
        *   报告数据拉取（调用甲方API）、处理（重复项标记、医学资料关联）、格式化。
        *   医学资料库查询服务。
    *   **数据存储层:**
        *   **乙方数据库:** 存储用户信息、设备ID与首次写入时间、以及转换后的医学资料库。
        *   **甲方系统:** 存储原始的检测仪数据和报告数据。
    *   **接口层:** 通过RESTful API进行前后端及与甲方系统的交互。

**3. 模块设计**

*   **3.1 二维码扫描与用户信息录入模块**
    *   **二维码生成服务 (API):**
        *   功能: 提供一个API接口，接收必要参数（如设备ID），生成包含H5用户信息填写页面URL的二维码图片或数据。
        *   技术: 后端语言实现，返回二维码图片流或Base64编码。
    *   **用户信息填写H5页面:**
        *   功能: 收集用户姓名、电话、出生年月日、性别、血型、设备ID。表单校验。提交数据到后端。
        *   技术: HTML5, CSS3, JavaScript (可选用轻量级前端框架如Vue.js/React的子集或原生JS)。
    *   **用户信息处理服务 (API):**
        *   功能:接收H5页面提交的用户数据，进行校验，存入乙方数据库（记录设备ID和首次写入时间），并调用甲方API将用户信息写入检测仪系统。
        *   技术: 后端语言实现。

*   **3.2 用户数据存储模块**
    *   **数据库设计:**
        *   `users` 表: 存储用户信息 (id, name, phone, dob, gender, blood_type, created_at)。
        *   `device_logs` 表: 存储设备ID与首次写入时间关联 (id, device_id, user_id, submission_time)。
    *   **数据访问服务 (API):**
        *   功能: 提供对用户数据和设备记录的增删改查接口（主要为增、查）。
        *   性能: 针对1000台并发写入场景，需考虑数据库连接池、写入优化（如批量写入，若适用）、索引优化。
    *   **部署:** 该模块的数据库和API服务部署在甲方服务器。

*   **3.3 报告查询与展示模块**
    *   **报告数据获取服务 (API):**
        *   功能: 调用甲方提供的API，拉取JSON格式的体检报告。
    *   **报告数据处理服务:**
        *   功能:
            *   **重复项标记:** 遍历报告JSON中的`filter_project`，识别`name`和`d_value`都相同的项目，并添加标记。
            *   **医学资料关联:** 根据项目`name`，查询已处理的医学资料库，获取对应的“中度症状”和“重度症状”。
        *   技术: 后端逻辑处理。
    *   **报告展示H5页面:**
        *   功能: 将处理后的报告数据，按甲方公司模板进行可视化展示。页面大小自适应。UI美观。展示重复项标记和关联的医学诊断信息。
        *   技术: HTML5, CSS3, JavaScript (同3.1.2)。

*   **3.4 医学资料库模块**
    *   **数据源处理:**
        *   初始数据: 甲方提供的Excel表格。
        *   处理方式: 将Excel数据清洗、转换为结构化数据（如JSON文件或导入数据库表）。推荐导入数据库表以方便查询和维护。
        *   `medical_knowledge` 表: (id, organ_name, moderate_symptom, severe_symptom, source_excel_row_id)。
    *   **资料查询服务 (API):**
        *   功能: 提供一个内部API，根据“组织器官”名称查询对应的“中度症状”和“重度症状”。
        *   技术: 后端语言实现，直接查询数据库或结构化文件。

*   **3.5 部署/联调/售后模块 (流程与职责)**
    *   **源码管理:** 使用Git进行版本控制。
    *   **联调:** 乙方负责开发和单元测试，与甲方配合进行集成测试和UAT。
    *   **部署:** 乙方提供部署脚本或说明，协助甲方完成在指定服务器上的部署。
    *   **售后:** 交付后7天内，针对已实现功能的Bug修复和样式微调。

**4. 数据设计**

*   **4.1 数据库选型:**
    *   关系型数据库，如 MySQL 或 PostgreSQL。选择需考虑甲方服务器环境的兼容性和运维能力。
*   **4.2 核心数据表 (已在模块设计中提及):**
    *   `users`
    *   `device_logs`
    *   `medical_knowledge`
*   **4.3 数据流:**
    1.  用户扫码 -> H5用户信息填写页。
    2.  用户提交信息 -> 后端API -> 存入乙方`users`表, `device_logs`表 -> 调用甲方API写入检测仪。
    3.  用户访问报告页 -> 后端API调用甲方报告API获取JSON -> 后端处理（重复项、医学资料） -> 返回给H5页面展示。

**5. 技术栈选型 (建议)**

*   **前端:**
    *   HTML5, CSS3, JavaScript (ES6+)
    *   可选框架: Vue.js (轻量级，上手快) 或 React (生态好，组件化)。若页面简单，原生JS亦可。
    *   UI库: 根据“美观”要求，可选用Element UI (Vue), Ant Design (React), Bootstrap，或自定义CSS。
    *   HTTP客户端: Axios 或 Fetch API。
*   **后端:**
    *   语言/框架:
        *   Node.js + Express.js/Koa.js (JavaScript全栈，适合I/O密集型应用)
        *   Python + Flask/Django (开发效率高，生态丰富)
        *   Java + Spring Boot (稳定，适合大型企业应用，但对个人兼职可能较重)
    *   API标准: RESTful API，使用JSON作为数据交换格式。
*   **数据库:** MySQL / PostgreSQL。
*   **服务器与部署:**
    *   Web服务器/反向代理: Nginx。
    *   容器化 (推荐): Docker，便于环境一致性和快速部署。
    *   操作系统: Linux (甲方服务器环境主流选择)。
*   **版本控制:** Git。

**6. 部署方案**

*   **部署环境:** 甲方提供的服务器。需明确服务器操作系统、资源配置、网络环境。
*   **部署组件:**
    *   后端应用服务 (Node.js/Python/Java应用)。
    *   乙方数据库 (MySQL/PostgreSQL)。
    *   前端H5静态资源 (HTML, CSS, JS, 图片等)。
*   **部署方式:**
    1.  **后端服务和数据库:**
        *   推荐使用Docker容器化部署，提供Dockerfile和docker-compose.yml。
        *   数据库需初始化表结构和导入医学资料库数据。
    2.  **前端静态资源:**
        *   打包构建后的静态文件，部署到Nginx等Web服务器下。
    3.  **Nginx配置:**
        *   配置反向代理，将特定路径的请求转发到后端应用服务。
        *   配置静态资源服务。
        *   配置HTTPS (若需要)。
*   **联调与上线流程:**
    1.  乙方完成开发和本地测试。
    2.  在甲方测试环境部署。
    3.  乙方与甲方进行接口联调和功能测试。
    4.  UAT (用户验收测试)。
    5.  正式环境部署上线。

**7. 非功能性需求**

*   **性能:** 用户数据存储模块需支持至少1000台设备并发写入的初步考虑。报告查询响应速度应在可接受范围内（如3-5秒内）。
*   **安全性:**
    *   对用户输入进行XSS等基本过滤。
    *   API接口考虑基本的安全措施（如HTTPS）。
    *   与甲方API交互的敏感信息（如有）需妥善处理。
*   **可维护性:** 代码结构清晰，注释良好，提供源码。模块化设计。
*   **易用性:** H5页面操作流程简洁明了，自适应不同屏幕尺寸，UI符合“美观”要求。
*   **可靠性:** 系统能稳定运行，数据存储准确无误。

**8. 风险与挑战**

*   **甲方API依赖:** 甲方API的稳定性、性能和文档清晰度直接影响项目进度和质量。
*   **医学资料库质量:** Excel数据的规范性、准确性、完整性会影响数据处理和查询结果。
*   **“美观”的主观性:** 需要与甲方就UI设计风格和标准达成一致。
*   **部署环境差异:** 甲方服务器环境可能存在的未知限制或配置问题。
*   **并发性能调优:** 1000台并发写入场景可能需要细致的性能测试和调优。

本总体方案为后续详细设计和开发提供了指导框架。在项目实施过程中，可根据实际情况进行调整和细化。