/**
 * SQL数据库服务
 * 在Windows环境下实现数据与SQL Server/SQLite的同步功能
 */

const fs = require('fs');
const path = require('path');
const util = require('util');
const sqlite3 = require('sqlite3').verbose();
const sql = require('mssql');

// 将fs的异步操作转换为Promise
const readFileAsync = util.promisify(fs.readFile);
const existsAsync = util.promisify(fs.exists);

// 数据库配置
const DB_CONFIG = {
  // SQL Server配置
  sqlserver: {
    enabled: false, // 是否启用SQL Server
    config: {
      user: 'sa',
      password: 'Password123',
      server: 'localhost',
      database: 'HealthCheckDB',
      options: {
        encrypt: false, // 使用windows认证时设置为true
        trustServerCertificate: true,
        enableArithAbort: true
      }
    }
  },
  // SQLite配置
  sqlite: {
    enabled: true, // 默认启用SQLite
    dbPath: path.join(__dirname, '../data/health_check.db')
  }
};

// 数据库连接池
let sqlServerPool = null;
let sqliteDb = null;

/**
 * 初始化数据库服务
 */
async function initDatabaseService() {
  console.log('Initializing database service...');

  try {
    // 初始化SQL Server连接池
    if (DB_CONFIG.sqlserver.enabled) {
      sqlServerPool = await new sql.ConnectionPool(DB_CONFIG.sqlserver.config).connect();
      console.log('SQL Server connection successful');

      // 创建SQL Server表结构
      await createSqlServerTables();
    }

    // 初始化SQLite数据库
    if (DB_CONFIG.sqlite.enabled) {
      // 确保SQLite数据库目录存在
      const dbDir = path.dirname(DB_CONFIG.sqlite.dbPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      // 检查数据库文件是否存在
      const dbExists = fs.existsSync(DB_CONFIG.sqlite.dbPath);

      // 创建SQLite数据库连接
      sqliteDb = new sqlite3.Database(DB_CONFIG.sqlite.dbPath, (err) => {
        if (err) {
          console.error('Failed to connect to SQLite database:', err);
        } else {
          console.log('SQLite database connection successful');

          // 如果数据库文件已存在，检查并更新表结构
          if (dbExists) {
            console.log('Existing database detected, checking for schema updates...');
            checkAndUpdateSqliteSchema();
          } else {
            // 创建新的SQLite表结构
            console.log('Creating new SQLite database schema...');
            createSqliteTables();
          }
        }
      });
    }

    return true;
  } catch (error) {
    console.error('Failed to initialize database service:', error);
    return false;
  }
}

/**
 * 检查并更新SQLite数据库表结构
 */
function checkAndUpdateSqliteSchema() {
  if (!sqliteDb) return;

  console.log('Checking SQLite database schema for users table...');

  sqliteDb.all("PRAGMA table_info(users)", (err, columns) => {
    if (err) {
      console.error('Error getting table columns for users:', err);
      return;
    }

    console.log('Current users table columns:', columns.map(col => col.name).join(', '));

    const hasDeviceId = columns.some(col => col.name === 'device_id');
    const hasTestType = columns.some(col => col.name === 'test_type');
    const hasAiFixTimes = columns.some(col => col.name === 'ai_fix_times');
    const hasAiReport = columns.some(col => col.name === 'ai_report');
    const hasSubmissionTime = columns.some(col => col.name === 'submission_time');
    const hasSessionId = columns.some(col => col.name === 'session_id');

    if (!hasDeviceId || !hasTestType || !hasAiFixTimes || !hasAiReport || !hasSubmissionTime || !hasSessionId) {
      console.log('Schema update needed for users table. Missing columns detected.');
      migrateSqliteSchema(hasDeviceId, hasTestType, hasAiFixTimes, hasAiReport, hasSubmissionTime, hasSessionId);
    } else {
      console.log('SQLite users table schema is up to date. Checking for device_id index...');
      sqliteDb.all("PRAGMA index_list(users)", (err, rows) => {
        if (err) {
            console.error('Error checking SQLite indexes for users table:', err);
            return;
        }
        const hasDeviceIdIndex = rows && rows.some(idx => idx.name === 'idx_users_device_id');
        if (!hasDeviceIdIndex) {
            console.log('Device_id index missing for users table. Creating index...');
            sqliteDb.run("CREATE INDEX IF NOT EXISTS idx_users_device_id ON users (device_id)", (err) => {
                if (err) {
                    console.error('Error creating device_id index for users table:', err);
                } else {
                    console.log('Device_id index created successfully for users table.');
                }
            });
        } else {
            console.log('Device_id index already exists for users table.');
        }
      });
    }
  });
}

/**
 * 迁移SQLite数据库表结构
 */
function migrateSqliteSchema(hasDeviceId, hasTestType, hasAiFixTimes, hasAiReport, hasSubmissionTime, hasSessionId) {
  if (!sqliteDb) return;

  console.log('Starting SQLite schema migration for users table...');

  sqliteDb.serialize(() => {
    sqliteDb.run("BEGIN TRANSACTION", (err) => {
      if (err) {
        console.error('Error starting transaction:', err);
        return;
      }
    });

    const alterStatements = [];

    if (!hasDeviceId) {
      alterStatements.push("ALTER TABLE users ADD COLUMN device_id TEXT");
    }
    if (!hasTestType) {
      alterStatements.push("ALTER TABLE users ADD COLUMN test_type TEXT");
    }
    if (!hasAiFixTimes) {
      alterStatements.push("ALTER TABLE users ADD COLUMN ai_fix_times TEXT");
    }
    if (!hasAiReport) {
      alterStatements.push("ALTER TABLE users ADD COLUMN ai_report TEXT");
    }
    if (!hasSubmissionTime) {
      alterStatements.push("ALTER TABLE users ADD COLUMN submission_time TEXT");
    }
    if (!hasSessionId) {
      alterStatements.push("ALTER TABLE users ADD COLUMN session_id TEXT");
    }

    let completedOperations = 0;
    const totalOperations = alterStatements.length + 1;
    let transactionFailed = false;

    function checkCompletion() {
      completedOperations++;
      if (completedOperations === totalOperations) {
        if (transactionFailed) {
          sqliteDb.run("ROLLBACK", (rollErr) => {
            if (rollErr) console.error('Error rolling back transaction:', rollErr);
            else console.log('Transaction rolled back due to errors during migration.');
          });
        } else {
          sqliteDb.run("COMMIT", (commitErr) => {
            if (commitErr) console.error('Error committing transaction:', commitErr);
            else console.log('Schema migration completed successfully for users table.');
          });
        }
      }
    }

    if (alterStatements.length > 0) {
        alterStatements.forEach((statement) => {
          console.log('Executing:', statement);
          sqliteDb.run(statement, (runErr) => {
            if (runErr) {
              console.error(`Error executing ${statement}:`, runErr);
              transactionFailed = true;
            }
          });
        });
    }
    if (alterStatements.length === 0) {
        totalOperations = 1;
    } else {
        for(let i=0; i < alterStatements.length; ++i) checkCompletion();
    }

    console.log('Creating device_id index if not exists for users table...');
    sqliteDb.run("CREATE INDEX IF NOT EXISTS idx_users_device_id ON users (device_id)", (idxErr) => {
      if (idxErr) {
        console.error('Error creating device_id index for users table:', idxErr);
        transactionFailed = true;
      } else {
        console.log('Device_id index creation step completed for users table.');
      }
      checkCompletion();
    });
    
    if (alterStatements.length === 0) {
    }
  });
}

/**
 * 创建SQL Server数据库表
 */
async function createSqlServerTables() {
  try {
    await sqlServerPool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' and xtype='U')
      CREATE TABLE users (
        id INT IDENTITY(1,1) PRIMARY KEY,
        name NVARCHAR(100) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        birthday VARCHAR(8),
        gender VARCHAR(10),
        blood_type VARCHAR(5),
        height VARCHAR(10),
        weight VARCHAR(10),
        device_id VARCHAR(255),
        test_type VARCHAR(20),
        ai_fix_times VARCHAR(10),
        ai_report VARCHAR(10),
        submission_time VARCHAR(50),
        session_id NVARCHAR(255),
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE(),
        CONSTRAINT UQ_Phone_Name UNIQUE (phone, name)
      )
    `);

    await sqlServerPool.request().query(`
      IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_DeviceId' AND object_id = OBJECT_ID('users'))
      BEGIN
        CREATE INDEX IX_Users_DeviceId ON users(device_id);
      END
    `);

    await sqlServerPool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='reports' and xtype='U')
      CREATE TABLE reports (
        id INT IDENTITY(1,1) PRIMARY KEY,
        user_id INT NOT NULL,
        report_date DATETIME NOT NULL,
        data NVARCHAR(MAX) NOT NULL,
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (user_id) REFERENCES users(id)
      )
    `);

    console.log('SQL Server tables (users, reports) and indexes created successfully');
  } catch (error) {
    console.error('Failed to create SQL Server tables or indexes:', error);
    throw error;
  }
}

/**
 * 创建SQLite数据库表
 */
function createSqliteTables() {
  if (!sqliteDb) return;

  sqliteDb.serialize(() => {
    sqliteDb.run(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT NOT NULL,
        birthday TEXT,
        gender TEXT,
        blood_type TEXT,
        height TEXT,
        weight TEXT,
        device_id TEXT,
        test_type TEXT,
        ai_fix_times TEXT,
        ai_report TEXT,
        submission_time TEXT,
        session_id TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(phone, name)
      )
    `);

    sqliteDb.run("CREATE INDEX IF NOT EXISTS idx_users_device_id ON users (device_id)");

    sqliteDb.run(`
      CREATE TABLE IF NOT EXISTS reports (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        report_date TIMESTAMP NOT NULL,
        data TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )
    `);

    console.log('SQLite tables (users, reports) and indexes created successfully');
  });
}

/**
 * 同步用户数据到数据库
 * @param {Object} userData - 用户数据对象
 * @returns {Promise<Object>} - 包含用户ID和是否新用户的对象
 */
async function syncUserToDatabase(userData) {
  try {
    let result = { userId: null, isNewUser: false };

    // 同步到SQL Server
    if (DB_CONFIG.sqlserver.enabled && sqlServerPool) {
      const sqlServerResult = await syncUserToSqlServer(userData);
      result = { ...result, ...sqlServerResult };
    }

    // 同步到SQLite
    if (DB_CONFIG.sqlite.enabled && sqliteDb) {
      const sqliteResult = await syncUserToSqlite(userData);

      // 如果SQL Server没有启用，使用SQLite的结果
      if (!DB_CONFIG.sqlserver.enabled) {
        result = { ...result, ...sqliteResult };
      }
    }

    return result;
  } catch (error) {
    console.error('Failed to sync user data to database:', error);
    throw error;
  }
}

/**
 * 同步用户数据到SQL Server
 * @param {Object} userData - 用户数据对象
 * @returns {Promise<Object>} - 包含用户ID和是否新用户的对象
 */
async function syncUserToSqlServer(userData) {
  try {
    const checkResult = await sqlServerPool.request()
      .input('phone', sql.VarChar, userData.phone)
      .input('name', sql.NVarChar, userData.name)
      .query('SELECT id FROM users WHERE phone = @phone AND name = @name');

    let userId = null;
    let isNewUser = false;

    if (checkResult.recordset.length > 0) {
      userId = checkResult.recordset[0].id;
      await sqlServerPool.request()
        .input('id', sql.Int, userId)
        .input('birthday', sql.VarChar, userData.birthday || null)
        .input('gender', sql.VarChar, userData.gender || null)
        .input('blood_type', sql.VarChar, userData.blood_type || null)
        .input('height', sql.VarChar, userData.height || null)
        .input('weight', sql.VarChar, userData.weight || null)
        .input('device_id', sql.VarChar, userData.device_id || null)
        .input('test_type', sql.VarChar, userData.test_type || null)
        .input('ai_fix_times', sql.VarChar, userData.ai_fix_times || null)
        .input('ai_report', sql.VarChar, userData.ai_report || null)
        .input('submission_time', sql.VarChar, userData.submission_time || null)
        .input('session_id', sql.NVarChar, userData.session_id || null)
        .input('updated_at', sql.DateTime, new Date())
        .query(`
          UPDATE users
          SET birthday = @birthday, gender = @gender, blood_type = @blood_type,
              height = @height, weight = @weight, device_id = @device_id,
              test_type = @test_type, ai_fix_times = @ai_fix_times,
              ai_report = @ai_report, submission_time = @submission_time,
              session_id = @session_id,
              updated_at = @updated_at
          WHERE id = @id
        `);
    } else {
      const insertResult = await sqlServerPool.request()
        .input('name', sql.NVarChar, userData.name)
        .input('phone', sql.VarChar, userData.phone)
        .input('birthday', sql.VarChar, userData.birthday || null)
        .input('gender', sql.VarChar, userData.gender || null)
        .input('blood_type', sql.VarChar, userData.blood_type || null)
        .input('height', sql.VarChar, userData.height || null)
        .input('weight', sql.VarChar, userData.weight || null)
        .input('device_id', sql.VarChar, userData.device_id || null)
        .input('test_type', sql.VarChar, userData.test_type || null)
        .input('ai_fix_times', sql.VarChar, userData.ai_fix_times || null)
        .input('ai_report', sql.VarChar, userData.ai_report || null)
        .input('submission_time', sql.VarChar, userData.submission_time || null)
        .input('session_id', sql.NVarChar, userData.session_id || null)
        .query(`
          INSERT INTO users (name, phone, birthday, gender, blood_type, height, weight, 
                             device_id, test_type, ai_fix_times, ai_report, submission_time, session_id)
          OUTPUT INSERTED.id
          VALUES (@name, @phone, @birthday, @gender, @blood_type, @height, @weight, 
                  @device_id, @test_type, @ai_fix_times, @ai_report, @submission_time, @session_id)
        `);
      userId = insertResult.recordset[0].id;
      isNewUser = true;
    }
    return { userId, isNewUser };
  } catch (error) {
    console.error('Failed to sync user data to SQL Server:', error);
    throw error;
  }
}

/**
 * 同步用户数据到SQLite
 * @param {Object} userData - 用户数据对象
 * @returns {Promise<Object>} - 包含用户ID和是否新用户的对象
 */
async function syncUserToSqlite(userData) {
  return new Promise((resolve, reject) => {
    sqliteDb.get(
      'SELECT id FROM users WHERE phone = ? AND name = ?',
      [userData.phone, userData.name],
      (err, row) => {
        if (err) return reject(err);

        let userId = null;
        let isNewUser = false;

        if (row) {
          userId = row.id;
          sqliteDb.run(
            `UPDATE users
             SET birthday = ?, gender = ?, blood_type = ?, height = ?, weight = ?,
                 device_id = ?, test_type = ?, ai_fix_times = ?, ai_report = ?,
                 submission_time = ?, session_id = ?, updated_at = CURRENT_TIMESTAMP
             WHERE id = ?`,
            [
              userData.birthday || null, userData.gender || null, 
              userData.blood_type || null, userData.height || null, userData.weight || null,
              userData.device_id || null, userData.test_type || null,
              userData.ai_fix_times || null, userData.ai_report || null,
              userData.submission_time || null,
              userData.session_id || null,
              userId
            ],
            function(updateErr) {
              if (updateErr) return reject(updateErr);
              resolve({ userId, isNewUser });
            }
          );
        } else {
          sqliteDb.run(
            `INSERT INTO users (name, phone, birthday, gender, blood_type, height, weight, 
                               device_id, test_type, ai_fix_times, ai_report, submission_time, session_id)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              userData.name, userData.phone, userData.birthday || null,
              userData.gender || null, userData.blood_type || null, userData.height || null,
              userData.weight || null, userData.device_id || null, userData.test_type || null,
              userData.ai_fix_times || null, userData.ai_report || null,
              userData.submission_time || null,
              userData.session_id || null
            ],
            function(insertErr) {
              if (insertErr) return reject(insertErr);
              userId = this.lastID;
              isNewUser = true;
              resolve({ userId, isNewUser });
            }
          );
        }
      }
    );
  });
}

/**
 * 同步报告数据到数据库
 * @param {number} userId - 用户ID
 * @param {Object} reportData - 报告数据对象
 * @returns {Promise<boolean>} - 是否成功同步
 */
async function syncReportToDatabase(userId, reportData) {
  try {
    const reportDate = new Date();

    // 同步到SQL Server
    if (DB_CONFIG.sqlserver.enabled && sqlServerPool) {
      await syncReportToSqlServer(userId, reportData, reportDate);
    }

    // 同步到SQLite
    if (DB_CONFIG.sqlite.enabled && sqliteDb) {
      await syncReportToSqlite(userId, reportData, reportDate);
    }

    return true;
  } catch (error) {
    console.error('Failed to sync report data to database:', error);
    return false;
  }
}

/**
 * 同步报告数据到SQL Server
 * @param {number} userId - 用户ID
 * @param {Object} reportData - 报告数据对象
 * @param {Date} reportDate - 报告日期
 */
async function syncReportToSqlServer(userId, reportData, reportDate) {
  try {
    // 将报告数据转换为JSON字符串
    const reportJson = JSON.stringify(reportData);

    // 插入或更新报告数据
    await sqlServerPool.request()
      .input('user_id', sql.Int, userId)
      .input('report_date', sql.DateTime, reportDate)
      .input('data', sql.NVarChar(sql.MAX), reportJson)
      .query(`
        MERGE reports AS target
        USING (SELECT @user_id as user_id, @report_date as report_date) AS source
        ON (target.user_id = source.user_id AND CONVERT(DATE, target.report_date) = CONVERT(DATE, source.report_date))
        WHEN MATCHED THEN
          UPDATE SET data = @data, updated_at = GETDATE()
        WHEN NOT MATCHED THEN
          INSERT (user_id, report_date, data)
          VALUES (@user_id, @report_date, @data);
      `);

    console.log('Report data synced to SQL Server successfully');
  } catch (error) {
    console.error('Failed to sync report data to SQL Server:', error);
    throw error;
  }
}

/**
 * 同步报告数据到SQLite
 * @param {number} userId - 用户ID
 * @param {Object} reportData - 报告数据对象
 * @param {Date} reportDate - 报告日期
 */
async function syncReportToSqlite(userId, reportData, reportDate) {
  return new Promise((resolve, reject) => {
    // 将报告数据转换为JSON字符串
    const reportJson = JSON.stringify(reportData);
    const dateStr = reportDate.toISOString().split('T')[0];

    // 先检查当天是否已有报告
    sqliteDb.get(
      "SELECT id FROM reports WHERE user_id = ? AND date(report_date) = date(?)",
      [userId, dateStr],
      (err, row) => {
        if (err) {
          reject(err);
          return;
        }

        if (row) {
          // 更新现有报告
          sqliteDb.run(
            `UPDATE reports
             SET data = ?, updated_at = CURRENT_TIMESTAMP
             WHERE id = ?`,
            [reportJson, row.id],
            function(err) {
              if (err) {
                reject(err);
                return;
              }
              console.log('Report data updated in SQLite successfully');
              resolve(true);
            }
          );
        } else {
          // 插入新报告
          sqliteDb.run(
            `INSERT INTO reports (user_id, report_date, data)
             VALUES (?, ?, ?)`,
            [userId, reportDate.toISOString(), reportJson],
            function(err) {
              if (err) {
                reject(err);
                return;
              }
              console.log('Report data added to SQLite successfully');
              resolve(true);
            }
          );
        }
      }
    );
  });
}

/**
 * 根据手机号和姓名获取用户ID
 * @param {string} phone - 用户手机号
 * @param {string} name - 用户姓名
 * @returns {Promise<number|null>} - 用户ID，如果用户不存在则返回null
 */
async function getUserIdByPhoneAndName(phone, name) {
  try {
    // 从SQL Server获取
    if (DB_CONFIG.sqlserver.enabled && sqlServerPool) {
      const result = await sqlServerPool.request()
        .input('phone', sql.VarChar, phone)
        .input('name', sql.NVarChar, name)
        .query('SELECT id FROM users WHERE phone = @phone AND name = @name');

      if (result.recordset.length > 0) {
        return result.recordset[0].id;
      }
    }

    // 从SQLite获取
    if (DB_CONFIG.sqlite.enabled && sqliteDb) {
      return new Promise((resolve, reject) => {
        sqliteDb.get(
          'SELECT id FROM users WHERE phone = ? AND name = ?',
          [phone, name],
          (err, row) => {
            if (err) {
              reject(err);
              return;
            }

            if (row) {
              resolve(row.id);
            } else {
              resolve(null);
            }
          }
        );
      });
    }

    return null;
  } catch (error) {
    console.error('Failed to get user ID:', error);
    return null;
  }
}

/**
 * 获取用户的最新报告数据
 * @param {number} userId - 用户ID
 * @returns {Promise<Object|null>} - 报告数据对象，如果没有报告则返回null
 */
async function getLatestReportByUserId(userId) {
  try {
    // 从SQL Server获取
    if (DB_CONFIG.sqlserver.enabled && sqlServerPool) {
      const result = await sqlServerPool.request()
        .input('user_id', sql.Int, userId)
        .query(`
          SELECT TOP 1 data
          FROM reports
          WHERE user_id = @user_id
          ORDER BY report_date DESC
        `);

      if (result.recordset.length > 0) {
        return JSON.parse(result.recordset[0].data);
      }
    }

    // 从SQLite获取
    if (DB_CONFIG.sqlite.enabled && sqliteDb) {
      return new Promise((resolve, reject) => {
        sqliteDb.get(
          `SELECT data FROM reports
           WHERE user_id = ?
           ORDER BY report_date DESC
           LIMIT 1`,
          [userId],
          (err, row) => {
            if (err) {
              reject(err);
              return;
            }

            if (row) {
              resolve(JSON.parse(row.data));
            } else {
              resolve(null);
            }
          }
        );
      });
    }

    return null;
  } catch (error) {
    console.error('Failed to get latest report data:', error);
    return null;
  }
}

/**
 * 关闭数据库连接
 */
async function closeDatabaseConnections() {
  // 关闭SQL Server连接池
  if (sqlServerPool) {
    try {
      await sqlServerPool.close();
      console.log('SQL Server connection pool closed');
    } catch (error) {
      console.error('Failed to close SQL Server connection pool:', error);
    }
  }

  // 关闭SQLite数据库连接
  if (sqliteDb) {
    sqliteDb.close((err) => {
      if (err) {
        console.error('Failed to close SQLite database connection:', err);
      } else {
        console.log('SQLite database connection closed');
      }
    });
  }
}

/**
 * 获取SQLite数据库实例
 * @returns {Object|null} SQLite数据库实例或null
 */
function getSqliteDb() {
  return sqliteDb;
}

module.exports = {
  initDatabaseService,
  syncUserToDatabase,
  syncReportToDatabase,
  getUserIdByPhoneAndName,
  getLatestReportByUserId,
  closeDatabaseConnections,
  getSqliteDb
};