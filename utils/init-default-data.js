/**
 * Default data initialization script
 * Adds sample user '<PERSON>' to the database
 */

const fs = require('fs');
const path = require('path');
const util = require('util');
const dbService = require('./database-service');

// Convert fs async operations to Promises
const writeFileAsync = util.promisify(fs.writeFile);
const mkdirAsync = util.promisify(fs.mkdir);
const existsAsync = util.promisify(fs.exists);

// Data directories
const dataDir = path.join(__dirname, '../data');
const usersDir = path.join(dataDir, 'users');

// <PERSON>'s sample data
const zhangSanData = {
  name: "张三",
  phone: "13800138000",
  birthday: "19900101",
  gender: "Male",
  blood_type: "A",
  height: "175",
  weight: "70"
};

// We no longer auto-generate report data
// Report data should only be synced from external API
const reportData = null;

/**
 * Save user data to filesystem
 */
async function saveToFilesystem() {
  console.log("Saving <PERSON>'s data to filesystem...");

  try {
    // Ensure directories exist
    if (!fs.existsSync(dataDir)) {
      await mkdirAsync(dataDir);
    }

    if (!fs.existsSync(usersDir)) {
      await mkdirAsync(usersDir);
    }

    // Create user folder
    const userFolderName = `${zhangSanData.phone}_${zhangSanData.name}`;
    const userFolderPath = path.join(usersDir, userFolderName);

    if (!fs.existsSync(userFolderPath)) {
      await mkdirAsync(userFolderPath);
    }

    // Save user info JSON file
    const userInfoPath = path.join(userFolderPath, 'user_info.json');
    await writeFileAsync(userInfoPath, JSON.stringify(zhangSanData, null, 2), 'utf8');

    // We no longer auto-generate report data files
    console.log("Note: Report data will not be auto-generated. It should be synced from external API.");

    console.log("Filesystem data saved successfully.");

    // Update users.csv file (simplified approach)
    const usersFilePath = path.join(dataDir, 'users.csv');
    const csvHeader = Object.keys(zhangSanData).join(',');
    const csvValues = Object.values(zhangSanData).join(',');
    const csvContent = `${csvHeader}\n${csvValues}`;

    await writeFileAsync(usersFilePath, csvContent, 'utf8');
    console.log("Users CSV file updated.");

    return true;
  } catch (error) {
    console.error("Error saving data to filesystem:", error);
    return false;
  }
}

/**
 * Save data to database
 */
async function saveToDatabase() {
  console.log("Initializing database service...");

  try {
    // Initialize database
    await dbService.initDatabaseService();

    console.log("Saving Zhang San's data to database...");

    // Save user data
    const dbResult = await dbService.syncUserToDatabase(zhangSanData);
    console.log(`User data synced to database. User ID: ${dbResult.userId}, Is new user: ${dbResult.isNewUser}`);

    // We no longer auto-generate report data
    console.log("Note: Report data will not be auto-generated in the database. It should be synced from external API.");

    // Close database connections
    await dbService.closeDatabaseConnections();
    console.log("Database connections closed.");

    return true;
  } catch (error) {
    console.error("Error saving data to database:", error);
    return false;
  }
}

/**
 * Initialize default data
 */
async function initDefaultData() {
  console.log("Initializing default data...");

  // Save to filesystem
  const fileResult = await saveToFilesystem();

  // Save to database
  const dbResult = await saveToDatabase();

  if (fileResult && dbResult) {
    console.log("Default data initialization completed successfully.");
    return true;
  } else {
    console.error("Default data initialization completed with errors.");
    return false;
  }
}

// Execute if run directly
if (require.main === module) {
  initDefaultData()
    .then(result => {
      if (result) {
        console.log("Script completed successfully.");
        process.exit(0);
      } else {
        console.error("Script completed with errors.");
        process.exit(1);
      }
    })
    .catch(err => {
      console.error("Script failed:", err);
      process.exit(1);
    });
} else {
  // Export for use in other modules
  module.exports = {
    initDefaultData,
    zhangSanData,
    reportData
  };
}