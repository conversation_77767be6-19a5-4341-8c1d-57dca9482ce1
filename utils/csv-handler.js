/**
 * CSV处理工具
 * 处理用户数据的CSV文件
 */

/**
 * 将对象数组转换为CSV字符串
 * @param {Array<Object>} data - 对象数组
 * @returns {string} CSV字符串
 */
function objectsToCSV(data) {
  if (!data || !data.length) return '';

  // 获取所有对象的键
  const headers = Object.keys(data[0]);

  // 创建CSV头行
  const headerRow = headers.join(',');

  // 创建数据行
  const rows = data.map(obj => {
    return headers.map(header => {
      // 处理包含逗号、引号或换行符的值
      const value = obj[header] === null || obj[header] === undefined ? '' : obj[header].toString();
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    }).join(',');
  });

  // 合并所有行
  return [headerRow, ...rows].join('\n');
}

/**
 * 将CSV字符串转换为对象数组
 * @param {string} csv - CSV字符串
 * @returns {Array<Object>} 对象数组
 */
function csvToObjects(csv) {
  if (!csv) return [];

  // 分割行
  const lines = csv.split('\n');
  if (lines.length < 2) return [];

  // 获取头部
  const headers = lines[0].split(',');

  // 解析数据行
  return lines.slice(1).filter(line => line.trim()).map(line => {
    const values = line.split(',');
    const obj = {};

    headers.forEach((header, index) => {
      obj[header] = values[index];
    });

    return obj;
  });
}

/**
 * 将用户数据保存为JSON文件
 * @param {Object} userData - 用户数据
 * @returns {Promise<boolean>} 是否成功
 */
async function saveUserData(userData) {
  try {
    // 首先检查用户是否已存在于users.csv中
    const existingUser = await checkUserExists(userData.phone, userData.name);

    // 如果用户不存在，则创建用户文件夹和保存用户数据
    if (!existingUser) {
      console.log(`用户 ${userData.name} (${userData.phone}) 不存在于users.csv中，将创建新用户数据`);

      try {
        // 使用服务器API保存用户数据
        console.log('使用服务器API保存用户数据');

        // 发送POST请求到服务器
        const response = await fetch('http://localhost:3005/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(userData)
        });

        if (!response.ok) {
          throw new Error(`服务器返回错误: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.message || '保存用户数据失败');
        }

        console.log('用户数据保存成功');
        console.log('注意：用户体检数据的JSON文件不会在此创建，只能从API同步获取');

        // 将用户数据保存到sessionStorage，用于前端显示
        sessionStorage.setItem(`user_info_${userData.phone}_${userData.name}`, JSON.stringify(userData));

        // 更新当前用户数据，用于报告展示
        sessionStorage.setItem('currentUser', JSON.stringify(userData));

        return true;
      } catch (error) {
        console.error('保存用户数据失败:', error);

        // 如果服务器API调用失败，回退到使用sessionStorage模拟
        console.log('服务器API调用失败，回退到使用sessionStorage模拟');

        // 将用户数据保存到sessionStorage
        sessionStorage.setItem(`user_info_${userData.phone}_${userData.name}`, JSON.stringify(userData));

        // 将用户数据添加到全局用户列表中
        await addUserToGlobalList(userData);

        return true;
      }
    } else {
      console.log(`用户 ${userData.name} (${userData.phone}) 已存在于users.csv中，不需要创建新用户数据`);
      return true;
    }
  } catch (error) {
    console.error('保存用户数据失败:', error);
    return false;
  }
}

/**
 * 检查用户是否已存在于users.csv中
 * @param {string} phone - 手机号
 * @param {string} name - 姓名
 * @returns {Promise<boolean>} 用户是否存在
 */
async function checkUserExists(phone, name) {
  try {
    // 获取现有用户列表
    let usersList = [];
    try {
      // 尝试从sessionStorage获取用户列表（仅用于模拟）
      const csvData = sessionStorage.getItem('usersListData');
      if (csvData) {
        usersList = csvToObjects(csvData);
      } else {
        // 如果sessionStorage中没有，尝试从文件获取
        const response = await fetch('../data/users.csv');
        if (response.ok) {
          const csvText = await response.text();
          usersList = csvToObjects(csvText);
          // 保存到sessionStorage以便后续使用
          sessionStorage.setItem('usersListData', csvText);
        }
      }
    } catch (error) {
      console.warn('读取用户列表失败:', error);
      return false; // 如果无法读取用户列表，假设用户不存在
    }

    // 检查用户是否已存在
    const existingUser = usersList.find(user => user.phone === phone && user.name === name);
    return !!existingUser; // 转换为布尔值
  } catch (error) {
    console.error('检查用户是否存在失败:', error);
    return false; // 如果发生错误，假设用户不存在
  }
}

/**
 * 将用户添加到全局用户列表
 * @param {Object} userData - 用户数据
 * @returns {Promise<boolean>} 是否成功
 */
async function addUserToGlobalList(userData) {
  try {
    // 获取现有用户列表
    let usersList = [];
    try {
      const response = await fetch('../data/users.csv');
      if (response.ok) {
        const csvText = await response.text();
        usersList = csvToObjects(csvText);
      }
    } catch (error) {
      console.warn('读取用户列表失败，创建新列表:', error);
    }

    // 检查用户是否已存在
    const existingUserIndex = usersList.findIndex(
      user => user.phone === userData.phone && user.name === userData.name
    );

    if (existingUserIndex >= 0) {
      // 更新现有用户
      usersList[existingUserIndex] = userData;
    } else {
      // 添加新用户
      usersList.push(userData);
    }

    // 转换为CSV
    const csvData = objectsToCSV(usersList);

    // 在实际应用中，这里应该是一个服务器端API调用来保存CSV文件
    // 但在前端JavaScript中，我们无法直接写入文件
    console.log('保存用户列表到: ../data/users.csv');
    console.log('用户列表数据:', csvData);

    // 为了在本地应用中模拟这个功能，我们将数据保存到sessionStorage
    // 这样在当前会话中可以访问这些数据
    sessionStorage.setItem('usersListData', csvData);

    return true;
  } catch (error) {
    console.error('添加用户到全局列表失败:', error);
    return false;
  }
}

/**
 * 查找用户数据
 * @param {string} phone - 手机号
 * @param {string} name - 姓名
 * @returns {Promise<Object|null>} 用户数据或null
 */
async function findUserData(phone, name) {
  try {
    console.log(`开始查找用户数据: ${name} (${phone})`);

    // 首先，直接检查用户文件夹是否存在
    // 这样即使CSV文件被锁定，我们仍然可以找到用户
    let userFolderName = `${phone}_${name}`;
    const userFolderPath = `../data/users/${userFolderName}`;

    console.log(`直接检查用户文件夹: ${userFolderPath}`);

    try {
      // 尝试读取用户信息文件
      const response = await fetch(`${userFolderPath}/user_info.json`);
      if (response.ok) {
        console.log(`找到用户文件夹和用户信息文件: ${userFolderPath}/user_info.json`);
        const userInfo = await response.json();
        console.log('用户信息:', userInfo);

        // 将用户信息保存到sessionStorage
        sessionStorage.setItem(`user_info_${phone}_${name}`, JSON.stringify(userInfo));

        return userInfo;
      } else {
        console.log(`未找到用户信息文件，尝试从CSV查找: ${userFolderPath}/user_info.json`);
      }
    } catch (error) {
      console.log(`检查用户文件夹失败，尝试从CSV查找: ${userFolderPath}`, error);
    }

    // 如果直接检查用户文件夹失败，尝试从CSV文件查找
    console.log('从CSV文件查找用户数据');

    // 从全局用户列表中查找用户
    let usersList = [];
    try {
      // 尝试从sessionStorage获取用户列表（仅用于模拟）
      const csvData = sessionStorage.getItem('usersListData');
      if (csvData) {
        console.log('从sessionStorage获取用户列表');
        usersList = csvToObjects(csvData);
      } else {
        // 如果sessionStorage中没有，尝试从文件获取
        console.log('从文件获取用户列表');
        try {
          const response = await fetch('../data/users.csv');
          if (response.ok) {
            const csvText = await response.text();
            usersList = csvToObjects(csvText);
            // 保存到sessionStorage以便后续使用
            sessionStorage.setItem('usersListData', csvText);
            console.log('成功从文件获取用户列表并保存到sessionStorage');
          } else {
            console.warn('获取CSV文件失败:', response.status, response.statusText);
          }
        } catch (fetchError) {
          console.warn('获取CSV文件失败:', fetchError);
        }
      }
    } catch (error) {
      console.warn('读取用户列表失败:', error);
    }

    // 查找匹配的用户
    console.log('查找用户:', name, phone);
    console.log('用户列表:', usersList);

    // 遍历用户列表，查找匹配的用户
    let userData = null;
    for (const user of usersList) {
      console.log('检查用户:', user.name, user.phone);
      if (user.phone === phone && user.name === name) {
        console.log('找到匹配的用户:', user);
        userData = user;
        break;
      }
    }

    if (userData) {
      console.log('从CSV文件找到用户数据');
      return userData;
    }

    console.warn(`未找到匹配的用户: ${name} (${phone})`);
    return null;

    // 构建用户文件夹路径
    let userFolderName = `${phone}_${name}`;

    const userFolderPath = `../data/users/${userFolderName}`;

    console.log(`找到用户数据: ${userFolderPath}/user_info.json`);

    return userData;
  } catch (error) {
    console.error('查找用户数据失败:', error);
    return null;
  }
}
