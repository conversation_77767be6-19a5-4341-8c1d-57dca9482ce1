/**
 * 从API或本地文件获取报告数据
 * @param {string} phone - 用户手机号
 * @param {string} name - 用户姓名
 * @returns {Promise<Object|null>} 报告数据或null（如果未找到）
 */
async function fetchReportData(phone, name) {
  try {
    // 获取当前时间戳，用于防止缓存
    const timestamp = new Date().getTime();

    // 构建用户文件夹路径
    let userFolderName = `${phone}_${name}`;

    console.log('构建用户文件夹路径:', userFolderName);

    const userFolderPath = `../data/users/${userFolderName}`;
    const reportFilePath = `${userFolderPath}/report_data.json`;

    console.log('用户文件夹路径:', userFolderPath);
    console.log('报告数据文件路径:', reportFilePath);

    // 尝试从API获取数据
    // 在实际应用中，这里应该是一个真实的API URL，包含用户的手机号和姓名作为参数
    try {
      // 模拟API请求
      // 实际应用中应替换为真实的API端点
      const apiUrl = `https://api.example.com/reports?phone=${encodeURIComponent(phone)}&name=${encodeURIComponent(name)}&t=${timestamp}`;

      // 由于这是本地应用，我们模拟API请求
      // 在实际应用中，应该使用下面的代码：
      // const response = await fetch(apiUrl);

      // 尝试从服务器获取报告数据
      console.log(`尝试从服务器获取用户 ${name} (${phone}) 的报告数据`);

      try {
        // 发送GET请求到服务器
        const response = await fetch(`http://localhost:3005/api/reports/${phone}/${name}`);
        console.log('服务器响应状态:', response.status, response.statusText);

        if (response.ok) {
          const result = await response.json();

          if (result.success) {
            console.log('成功从服务器获取报告数据');
            return result.data;
          } else {
            console.warn(`服务器返回错误: ${result.message}`);
          }
        } else {
          console.warn(`服务器返回错误: ${response.status} ${response.statusText}`);
        }
      } catch (fetchError) {
        console.error(`从服务器获取报告数据失败:`, fetchError);
      }

      // 如果没有找到用户报告数据文件，检查sessionStorage
      const cachedData = sessionStorage.getItem(`report_${phone}_${name}`);
      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        console.log(`使用sessionStorage中的报告数据: ${reportFilePath}`, parsed.timestamp);
        return parsed.data;
      }

      // 如果在data文件夹和sessionStorage中都没有找到数据，返回null
      console.log(`未找到用户 ${name} (${phone}) 的报告数据`);
      console.log('用户体检数据的JSON只能从API同步获取，请等待API同步');

      // 返回null表示未找到数据，不使用本地示例文件作为备用
      return null;
    } catch (error) {
      console.warn('从API获取数据失败:', error);

      // 尝试从文件系统读取之前保存的报告数据
      try {
        // 在实际应用中，这里应该从文件系统读取
        // 但在前端JavaScript中，我们使用sessionStorage模拟
        const cachedData = sessionStorage.getItem(`report_${phone}_${name}`);
        if (cachedData) {
          const parsed = JSON.parse(cachedData);
          console.log(`API请求失败，使用之前保存的报告数据: ${reportFilePath}`, parsed.timestamp);
          return parsed.data;
        }
      } catch (readError) {
        console.warn('读取保存的报告数据失败:', readError);
      }
    }

    // 如果所有尝试都失败，返回null表示未找到数据
    console.log('所有尝试都失败，返回null表示未找到数据');
    return null;
  } catch (error) {
    console.error('获取报告数据失败:', error);
    return null;
  }
}

/**
 * 处理并显示报告数据
 * @param {Object} reportData - 报告数据
 */
function processAndDisplayReport(reportData) {
  const reportContainer = document.getElementById('report-container');

  // 清空容器
  reportContainer.innerHTML = '';

  // 添加基本信息
  const basicInfoSection = document.createElement('div');
  basicInfoSection.className = 'report-section';
  basicInfoSection.innerHTML = `
    <h2>基本信息</h2>
    <div class="report-item">
      <span>检测时间:</span>
      <span>${reportData.time || '未知'}</span>
    </div>
    <div class="report-item">
      <span>年龄:</span>
      <span>${reportData.age || '未知'}</span>
    </div>
    <div class="report-item">
      <span>性别:</span>
      <span>${reportData.sex || '未知'}</span>
    </div>
  `;
  reportContainer.appendChild(basicInfoSection);

  // 处理器官数据
  if (reportData.organ) {
    processSystemData('器官检测', reportData.organ, reportContainer);
  }

  // 处理生理系统数据
  if (reportData.Physiological_system) {
    // 呼吸系统
    if (reportData.Physiological_system.respiratory_system) {
      processSystemData('呼吸系统', reportData.Physiological_system.respiratory_system, reportContainer);
    }

    // 感觉系统
    if (reportData.Physiological_system.sensory_system) {
      processSystemData('感觉系统', reportData.Physiological_system.sensory_system, reportContainer);
    }

    // 泌尿生殖系统
    if (reportData.Physiological_system.urogenital_system) {
      processSystemData('泌尿生殖系统', reportData.Physiological_system.urogenital_system, reportContainer);
    }

    // 泌尿系统
    if (reportData.Physiological_system.urinary_system) {
      processSystemData('泌尿系统', reportData.Physiological_system.urinary_system, reportContainer);
    }

    // 消化系统
    if (reportData.Physiological_system.digestive_system) {
      processSystemData('消化系统', reportData.Physiological_system.digestive_system, reportContainer);
    }

    // 肌肉骨骼系统
    if (reportData.Physiological_system.musculoskeletal_system) {
      processSystemData('肌肉骨骼系统', reportData.Physiological_system.musculoskeletal_system, reportContainer);
    }
  }

  // 处理微生物数据
  if (reportData.microbial_worms) {
    processSystemData('微生物检测', reportData.microbial_worms, reportContainer);
  }

  // 处理食物数据
  if (reportData.foods) {
    const foodsSection = document.createElement('div');
    foodsSection.className = 'report-section';
    foodsSection.innerHTML = `<h2>推荐食物</h2>`;

    if (reportData.foods.filter_project && reportData.foods.filter_project.length > 0) {
      reportData.foods.filter_project.forEach(item => {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'report-item';
        itemDiv.innerHTML = `
          <span>${item.name}</span>
          <span>${item.d_value}</span>
        `;
        foodsSection.appendChild(itemDiv);
      });
    } else {
      foodsSection.innerHTML += '<p>无推荐食物数据</p>';
    }

    reportContainer.appendChild(foodsSection);
  }

  // 处理矿物质数据
  if (reportData.mineral) {
    const mineralSection = document.createElement('div');
    mineralSection.className = 'report-section';
    mineralSection.innerHTML = `<h2>推荐矿物质</h2>`;

    if (reportData.mineral.filter_project && reportData.mineral.filter_project.length > 0) {
      reportData.mineral.filter_project.forEach(item => {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'report-item';
        itemDiv.innerHTML = `
          <span>${item.name}</span>
          <span>${item.d_value}</span>
        `;
        mineralSection.appendChild(itemDiv);
      });
    } else {
      mineralSection.innerHTML += '<p>无推荐矿物质数据</p>';
    }

    reportContainer.appendChild(mineralSection);
  }
}

/**
 * 处理系统数据并添加到报告容器
 * @param {string} title - 系统标题
 * @param {Object} systemData - 系统数据
 * @param {HTMLElement} container - 容器元素
 */
function processSystemData(title, systemData, container) {
  const section = document.createElement('div');
  section.className = 'report-section';
  section.innerHTML = `<h2>${title}</h2>`;

  // 添加参考范围
  if (systemData.reference_range && systemData.reference_range.length > 0) {
    const rangeDiv = document.createElement('div');
    rangeDiv.className = 'section-title';
    rangeDiv.textContent = '参考范围';
    section.appendChild(rangeDiv);

    systemData.reference_range.forEach(item => {
      const itemDiv = document.createElement('div');
      itemDiv.className = 'report-item';

      // 根据结果添加不同的样式
      let severityClass = '';
      if (item.resule === '重') {
        severityClass = 'severity-high';
      } else if (item.resule === '中') {
        severityClass = 'severity-medium';
      } else if (item.resule === '低') {
        severityClass = 'severity-low';
      }

      itemDiv.innerHTML = `
        <span>数值: ${item.d_value}</span>
        <span class="${severityClass}">程度: ${item.resule}</span>
      `;
      section.appendChild(itemDiv);
    });
  }

  // 添加项目数据
  if (systemData.filter_project && systemData.filter_project.length > 0) {
    const projectDiv = document.createElement('div');
    projectDiv.className = 'section-title';
    projectDiv.textContent = '检测项目';
    section.appendChild(projectDiv);

    // 用于检测重复项
    const seenItems = new Map();

    systemData.filter_project.forEach(item => {
      const key = `${item.name}-${item.d_value}`;
      const isDuplicate = seenItems.has(key);

      if (!isDuplicate) {
        seenItems.set(key, true);
      }

      const itemDiv = document.createElement('div');
      itemDiv.className = `report-item ${isDuplicate ? 'duplicate-item' : ''}`;

      // 根据结果添加不同的样式
      let severityClass = '';
      if (item.resule === '重') {
        severityClass = 'severity-high';
      } else if (item.resule === '中') {
        severityClass = 'severity-medium';
      } else if (item.resule === '低') {
        severityClass = 'severity-low';
      }

      itemDiv.innerHTML = `
        <span>${item.name}</span>
        <span class="${severityClass}">${item.resule || ''} (${item.d_value})</span>
      `;

      // 添加医学资料关联（这里是模拟数据，实际应用中应该从数据库获取）
      if (item.resule === '重' || item.resule === '中') {
        const symptomDiv = document.createElement('div');
        symptomDiv.className = 'symptom-info';

        if (item.resule === '中') {
          symptomDiv.innerHTML = `
            <div class="symptom-title">中度症状:</div>
            <div>可能出现${item.name}相关的中度不适，建议注意观察。</div>
          `;
        } else if (item.resule === '重') {
          symptomDiv.innerHTML = `
            <div class="symptom-title">重度症状:</div>
            <div>可能出现${item.name}相关的严重症状，建议及时就医。</div>
          `;
        }

        itemDiv.appendChild(symptomDiv);
      }

      section.appendChild(itemDiv);
    });
  }

  container.appendChild(section);
}
