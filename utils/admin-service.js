/**
 * Database administration service
 * Provides functionality for managing SQLite/SQL Server databases
 */

const fs = require('fs');
const path = require('path');
const util = require('util');
const dbService = require('./database-service');

// Convert fs async operations to Promises
const readFileAsync = util.promisify(fs.readFile);
const writeFileAsync = util.promisify(fs.writeFile);
const copyFileAsync = util.promisify(fs.copyFile);
const mkdirAsync = util.promisify(fs.mkdir);
const existsAsync = util.promisify(fs.exists);
const readdirAsync = util.promisify(fs.readdir);

// Data directories
const dataDir = path.join(__dirname, '../data');
const usersDir = path.join(dataDir, 'users');
const backupsDir = path.join(dataDir, 'backups');

/**
 * Get database statistics
 * @returns {Promise<Object>} Database statistics
 */
async function getDatabaseStats() {
  try {
    const sqliteDb = dbService.getSqliteDb();
    if (!sqliteDb) {
      return {
        userCount: 0,
        reportCount: 0
      };
    }

    // Get user count
    const userCount = await new Promise((resolve, reject) => {
      sqliteDb.get('SELECT COUNT(*) as count FROM users', (err, row) => {
        if (err) reject(err);
        else resolve(row ? row.count : 0);
      });
    });

    // Get report count
    const reportCount = await new Promise((resolve, reject) => {
      sqliteDb.get('SELECT COUNT(*) as count FROM reports', (err, row) => {
        if (err) reject(err);
        else resolve(row ? row.count : 0);
      });
    });

    return {
      userCount,
      reportCount
    };
  } catch (error) {
    console.error('Failed to get database stats:', error);
    throw error;
  }
}

/**
 * Get all users from database
 * @returns {Promise<Array>} List of users
 */
async function getAllUsers() {
  try {
    const sqliteDb = dbService.getSqliteDb();
    if (!sqliteDb) {
      return [];
    }

    return new Promise((resolve, reject) => {
      sqliteDb.all('SELECT * FROM users ORDER BY id DESC', (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      });
    });
  } catch (error) {
    console.error('Failed to get users:', error);
    throw error;
  }
}

/**
 * Get user by ID
 * @param {number} userId - User ID
 * @returns {Promise<Object|null>} User object or null if not found
 */
async function getUserById(userId) {
  try {
    const sqliteDb = dbService.getSqliteDb();
    if (!sqliteDb) {
      return null;
    }

    return new Promise((resolve, reject) => {
      sqliteDb.get('SELECT * FROM users WHERE id = ?', [userId], (err, row) => {
        if (err) reject(err);
        else resolve(row || null);
      });
    });
  } catch (error) {
    console.error(`Failed to get user with ID ${userId}:`, error);
    throw error;
  }
}

/**
 * Get all reports from database with user information
 * @returns {Promise<Array>} List of reports
 */
async function getAllReports() {
  try {
    const sqliteDb = dbService.getSqliteDb();
    if (!sqliteDb) {
      return [];
    }

    return new Promise((resolve, reject) => {
      sqliteDb.all(`
        SELECT r.*, u.name as user_name 
        FROM reports r
        LEFT JOIN users u ON r.user_id = u.id
        ORDER BY r.id DESC
      `, (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      });
    });
  } catch (error) {
    console.error('Failed to get reports:', error);
    throw error;
  }
}

/**
 * Get report by ID
 * @param {number} reportId - Report ID
 * @returns {Promise<Object|null>} Report object or null if not found
 */
async function getReportById(reportId) {
  try {
    const sqliteDb = dbService.getSqliteDb();
    if (!sqliteDb) {
      return null;
    }

    const report = await new Promise((resolve, reject) => {
      sqliteDb.get(`
        SELECT r.*, u.name as user_name, u.phone as user_phone
        FROM reports r
        LEFT JOIN users u ON r.user_id = u.id
        WHERE r.id = ?
      `, [reportId], (err, row) => {
        if (err) reject(err);
        else resolve(row || null);
      });
    });

    if (report && report.data) {
      try {
        // Parse JSON data field
        report.data = JSON.parse(report.data);
      } catch (e) {
        console.warn(`Could not parse report data for report ID ${reportId}`);
      }
    }

    return report;
  } catch (error) {
    console.error(`Failed to get report with ID ${reportId}:`, error);
    throw error;
  }
}

/**
 * Delete user by ID (also deletes related reports)
 * @param {number} userId - User ID
 * @returns {Promise<boolean>} True if successful
 */
async function deleteUser(userId) {
  try {
    const sqliteDb = dbService.getSqliteDb();
    if (!sqliteDb) {
      return false;
    }

    // Get user info
    const user = await getUserById(userId);
    if (!user) {
      return false;
    }

    // Start a transaction
    return new Promise((resolve, reject) => {
      sqliteDb.serialize(() => {
        sqliteDb.run('BEGIN TRANSACTION');

        // Delete related reports
        sqliteDb.run('DELETE FROM reports WHERE user_id = ?', [userId], function(err) {
          if (err) {
            sqliteDb.run('ROLLBACK');
            reject(err);
            return;
          }

          // Delete user
          sqliteDb.run('DELETE FROM users WHERE id = ?', [userId], function(err) {
            if (err) {
              sqliteDb.run('ROLLBACK');
              reject(err);
              return;
            }

            // Commit transaction
            sqliteDb.run('COMMIT', function(err) {
              if (err) {
                sqliteDb.run('ROLLBACK');
                reject(err);
                return;
              }

              // Also delete user folder if exists
              const userFolderName = `${user.phone}_${user.name}`;
              const userFolderPath = path.join(usersDir, userFolderName);

              fs.rm(userFolderPath, { recursive: true, force: true }, (err) => {
                if (err) {
                  console.warn(`Could not delete user folder: ${userFolderPath}`, err);
                }
                resolve(true);
              });
            });
          });
        });
      });
    });
  } catch (error) {
    console.error(`Failed to delete user with ID ${userId}:`, error);
    throw error;
  }
}

/**
 * Delete report by ID
 * @param {number} reportId - Report ID
 * @returns {Promise<boolean>} True if successful
 */
async function deleteReport(reportId) {
  try {
    const sqliteDb = dbService.getSqliteDb();
    if (!sqliteDb) {
      return false;
    }

    return new Promise((resolve, reject) => {
      sqliteDb.run('DELETE FROM reports WHERE id = ?', [reportId], function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        resolve(this.changes > 0);
      });
    });
  } catch (error) {
    console.error(`Failed to delete report with ID ${reportId}:`, error);
    throw error;
  }
}

/**
 * Backup the SQLite database
 * @returns {Promise<Object>} Backup result with backup path
 */
async function backupDatabase() {
  try {
    // Get SQLite database path
    const dbPath = path.join(dataDir, 'health_check.db');
    
    // Check if database exists
    if (!await existsAsync(dbPath)) {
      throw new Error('Database file not found');
    }
    
    // Create backups directory if it doesn't exist
    if (!await existsAsync(backupsDir)) {
      await mkdirAsync(backupsDir, { recursive: true });
    }
    
    // Generate backup filename with timestamp
    const now = new Date();
    const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`;
    const backupPath = path.join(backupsDir, `health_check_${timestamp}.db`);
    
    // Copy database file
    await copyFileAsync(dbPath, backupPath);
    
    return {
      success: true,
      backupPath: `data/backups/health_check_${timestamp}.db`
    };
  } catch (error) {
    console.error('Failed to backup database:', error);
    throw error;
  }
}

/**
 * Sync data between file system and database
 * @returns {Promise<Object>} Sync result with count of synced items
 */
async function syncFileSystemAndDatabase() {
  try {
    if (!await existsAsync(usersDir)) {
      throw new Error('Users directory not found');
    }
    
    // Get all user folders
    const userFolders = await readdirAsync(usersDir);
    let syncedCount = 0;
    
    for (const folder of userFolders) {
      // Skip non-directories
      const folderPath = path.join(usersDir, folder);
      const stats = await fs.promises.stat(folderPath);
      if (!stats.isDirectory()) continue;
      
      // Parse folder name to get phone and name
      const [phone, name] = folder.split('_');
      if (!phone || !name) continue;
      
      // Read user info
      const userInfoPath = path.join(folderPath, 'user_info.json');
      if (!await existsAsync(userInfoPath)) continue;
      
      try {
        const userInfoData = await readFileAsync(userInfoPath, 'utf8');
        const userData = JSON.parse(userInfoData);
        
        // Sync user to database
        const result = await dbService.syncUserToDatabase(userData);
        
        // Read report data if exists
        const reportPath = path.join(folderPath, 'report_data.json');
        if (await existsAsync(reportPath)) {
          const reportDataText = await readFileAsync(reportPath, 'utf8');
          const reportData = JSON.parse(reportDataText);
          
          // Sync report to database
          if (result.userId) {
            await dbService.syncReportToDatabase(result.userId, reportData);
          }
        }
        
        syncedCount++;
      } catch (error) {
        console.error(`Error syncing user ${folder}:`, error);
      }
    }
    
    return {
      success: true,
      syncedCount
    };
  } catch (error) {
    console.error('Failed to sync file system and database:', error);
    throw error;
  }
}

module.exports = {
  getDatabaseStats,
  getAllUsers,
  getUserById,
  getAllReports,
  getReportById,
  deleteUser,
  deleteReport,
  backupDatabase,
  syncFileSystemAndDatabase
}; 