/**
 * 数据同步服务
 * 模拟定期从API同步数据的功能
 */

// 导入CSV处理函数

/**
 * 根据生日计算年龄
 * @param {string} birthday - 生日，格式为YYYYMMDD
 * @returns {number} 年龄
 */
function calculateAge(birthday) {
  if (!birthday || birthday.length !== 8) {
    return 30; // 默认年龄
  }

  try {
    const year = parseInt(birthday.substring(0, 4));
    const month = parseInt(birthday.substring(4, 6)) - 1; // 月份从0开始
    const day = parseInt(birthday.substring(6, 8));

    const birthDate = new Date(year, month, day);
    const today = new Date();

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    // 如果今年的生日还没过，减去一岁
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  } catch (error) {
    console.warn('计算年龄失败:', error);
    return 30; // 默认年龄
  }
}
function csvToObjects(csv) {
  if (!csv) return [];

  // 分割行
  const lines = csv.split('\n');
  if (lines.length < 2) return [];

  // 获取头部
  const headers = lines[0].split(',');

  // 解析数据行
  return lines.slice(1).filter(line => line.trim()).map(line => {
    const values = line.split(',');
    const obj = {};

    headers.forEach((header, index) => {
      obj[header] = values[index];
    });

    return obj;
  });
}

// 同步间隔（毫秒）
const SYNC_INTERVAL = 30 * 60 * 1000; // 30分钟

// 上次同步时间的存储键
const LAST_SYNC_KEY = 'last_sync_timestamp';

/**
 * 初始化同步服务
 */
function initSyncService() {
  console.log('初始化数据同步服务...');

  // 检查是否需要同步
  checkAndSync();

  // 设置定期检查
  setInterval(checkAndSync, 60000); // 每分钟检查一次
}

/**
 * 检查是否需要同步，如果需要则执行同步
 */
async function checkAndSync() {
  const lastSync = sessionStorage.getItem(LAST_SYNC_KEY);
  const now = Date.now();

  // 如果从未同步或者距离上次同步已经超过了同步间隔
  if (!lastSync || (now - parseInt(lastSync)) > SYNC_INTERVAL) {
    console.log('开始同步数据...');
    await syncData();
    sessionStorage.setItem(LAST_SYNC_KEY, now.toString());
    console.log('数据同步完成，时间：', new Date().toLocaleString());
  }
}

/**
 * 执行数据同步
 * 在实际应用中，这个函数应该从API获取最新数据
 */
async function syncData() {
  try {
    // 模拟API请求延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 尝试从data/users.csv文件获取用户列表
    let usersList = [];
    try {
      const response = await fetch('../data/users.csv');
      if (response.ok) {
        const csvText = await response.text();
        usersList = csvToObjects(csvText);
        console.log(`从CSV文件中读取到 ${usersList.length} 个用户记录`);
      } else {
        console.warn('无法读取用户列表CSV文件');

        // 尝试从sessionStorage获取
        const usersListData = sessionStorage.getItem('usersListData');
        if (usersListData) {
          usersList = csvToObjects(usersListData);
          console.log(`从sessionStorage中读取到 ${usersList.length} 个用户记录`);
        }
      }
    } catch (error) {
      console.warn('读取用户列表失败:', error);

      // 尝试从sessionStorage获取
      const usersListData = sessionStorage.getItem('usersListData');
      if (usersListData) {
        usersList = csvToObjects(usersListData);
        console.log(`从sessionStorage中读取到 ${usersList.length} 个用户记录`);
      }
    }

    if (usersList.length === 0) {
      console.log('没有找到用户列表数据，无法同步');
      return false;
    }

    console.log(`开始同步 ${usersList.length} 个用户的报告数据`);

    // 同步每个用户的报告数据
    for (const userData of usersList) {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 200));

      const { phone, name } = userData;
      if (!phone || !name) continue;

      // 构建用户文件夹路径
      let userFolderName = `${phone}_${name}`;

      const userFolderPath = `../data/users/${userFolderName}`;
      const reportFilePath = `${userFolderPath}/report_data.json`;

      // 检查用户文件夹是否存在
      try {
        // 尝试读取用户信息文件，判断用户文件夹是否存在
        const userInfoResponse = await fetch(`${userFolderPath}/user_info.json`);
        if (userInfoResponse.ok) {
          console.log(`找到用户文件夹: ${userFolderPath}`);

          // 用户体检数据的JSON只能从API同步获取，不检查是否已有报告数据文件
          console.log(`用户 ${name} (${phone}) 的体检数据需要从API同步获取`);

          // 模拟检查上次同步时间
          const lastSyncKey = `last_sync_${phone}_${name}`;
          const lastSync = sessionStorage.getItem(lastSyncKey);
          const now = Date.now();

          // 如果从未同步或者距离上次同步已经超过了同步间隔（30分钟）
          if (!lastSync || (now - parseInt(lastSync)) > 30 * 60 * 1000) {
            console.log(`用户 ${name} (${phone}) 需要同步体检数据`);
          } else {
            console.log(`用户 ${name} (${phone}) 最近已同步过体检数据，跳过同步`);
            continue; // 最近已同步，跳过
          }

          // 从API获取报告数据
          console.log(`从API获取用户 ${name} (${phone}) 的报告数据`);

          try {
            // 发送POST请求到服务器，同步报告数据
            const response = await fetch(`http://localhost:3005/api/sync/${phone}/${name}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              }
            });

            if (!response.ok) {
              throw new Error(`服务器返回错误: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();

            if (!result.success) {
              throw new Error(result.message || '同步报告数据失败');
            }

            console.log('报告数据同步成功');

            // 将报告数据保存到sessionStorage，用于前端显示
            sessionStorage.setItem(`report_${phone}_${name}`, JSON.stringify({
              timestamp: new Date().toISOString(),
              data: result.data,
              synced: true
            }));

            // 记录同步时间
            sessionStorage.setItem(`last_sync_${phone}_${name}`, now.toString());
          } catch (error) {
            console.error('同步报告数据失败:', error);

            // 如果服务器API调用失败，回退到使用sessionStorage模拟
            console.log('服务器API调用失败，回退到使用sessionStorage模拟');

            // 模拟从API获取的体检数据
            const syncTimestamp = new Date().toISOString();
            const reportData = {
              name: name,
              time: syncTimestamp,
              baseurl: "http://www.example.com/reports/" + syncTimestamp.split('T')[0],
              age: calculateAge(userData.birthday),
              sex: userData.gender,
              organ: {
                reference_range: [
                  { d_value: "4.291", resule: "重" },
                  { d_value: "3.150", resule: "中" },
                  { d_value: "2.075", resule: "低" }
                ],
                filter_project: [
                  { name: "肝脏", d_value: "3.842", resule: "中" },
                  { name: "肾脏", d_value: "2.953", resule: "中" },
                  { name: "心脏", d_value: "4.125", resule: "重" }
                ]
              }
            };

            // 保存体检数据
            sessionStorage.setItem(`report_${phone}_${name}`, JSON.stringify({
              timestamp: syncTimestamp,
              data: reportData,
              synced: true
            }));

            // 记录同步时间
            sessionStorage.setItem(`last_sync_${phone}_${name}`, now.toString());
          }
        } else {
          console.warn(`未找到用户文件夹: ${userFolderPath}`);
        }
      } catch (error) {
        console.warn(`检查用户文件夹失败: ${userFolderPath}`, error);
      }
    }

    return true;
  } catch (error) {
    console.error('数据同步失败:', error);
    return false;
  }
}

// 当页面加载时初始化同步服务
document.addEventListener('DOMContentLoaded', initSyncService);
