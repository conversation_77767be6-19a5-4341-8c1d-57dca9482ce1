<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>健康评估系统</title>
  <link rel="stylesheet" href="assets/css/main.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <style>
    .hero {
      background: linear-gradient(135deg, #4285f4, #34a853);
      color: white;
      padding: 60px 0;
      text-align: center;
      margin-bottom: 40px;
      border-radius: 0 0 10px 10px;
    }

    .hero h1 {
      font-size: 2.5rem;
      margin-bottom: 15px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .hero p {
      font-size: 1.2rem;
      max-width: 600px;
      margin: 0 auto;
      opacity: 0.9;
    }

    .features {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 30px;
      margin-top: 40px;
    }

    .feature-card {
      background-color: var(--card-background);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      padding: 30px;
      text-align: center;
      transition: var(--transition);
      cursor: pointer;
      text-decoration: none;
      color: var(--text-color);
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 100%;
    }

    .feature-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    }

    .feature-icon {
      font-size: 3rem;
      margin-bottom: 20px;
      color: var(--primary-color);
      background-color: rgba(66, 133, 244, 0.1);
      width: 100px;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      margin-bottom: 20px;
    }

    .feature-card:nth-child(2) .feature-icon {
      color: var(--secondary-color);
      background-color: rgba(52, 168, 83, 0.1);
    }

    .feature-card:nth-child(3) .feature-icon {
      color: var(--accent-color);
      background-color: rgba(251, 188, 5, 0.1);
    }

    .feature-title {
      font-size: 1.5rem;
      margin-bottom: 15px;
      color: var(--primary-color);
    }

    .feature-card:nth-child(2) .feature-title {
      color: var(--secondary-color);
    }

    .feature-card:nth-child(3) .feature-title {
      color: var(--accent-color);
    }

    .feature-description {
      color: var(--light-text);
      line-height: 1.6;
    }

    @media (max-width: 768px) {
      .features {
        grid-template-columns: 1fr;
      }

      .hero {
        padding: 40px 0;
      }

      .hero h1 {
        font-size: 2rem;
      }
    }

    footer {
      margin-top: 40px;
      text-align: center;
      color: #666;
    }
    .admin-link {
      color: #999;
      font-size: 0.8em;
      text-decoration: none;
    }
    .admin-link:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container">
      <a href="index.html" class="navbar-logo">健康评估系统</a>
      <ul class="navbar-menu">
        <li class="navbar-item"><a href="index.html" class="navbar-link active">首页</a></li>
        <li class="navbar-item"><a href="qrcode/index.html?device_id=0&session_id=0" class="navbar-link">生成二维码</a></li>
        <li class="navbar-item"><a href="form/index.html" class="navbar-link">信息填写</a></li>
        <li class="navbar-item"><a href="report/index.html" class="navbar-link">查询报告</a></li>
      </ul>
    </div>
  </nav>

  <!-- 主要内容 -->
  <div class="hero">
    <div class="container">
      <h1>健康评估系统</h1>
      <p>全面的健康检测解决方案，帮助您更好地了解和管理自己的健康状况</p>
    </div>
  </div>

  <div class="container">
    <div class="features">
      <a href="qrcode/index.html?device_id=0&session_id=0" class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-qrcode"></i>
        </div>
        <h2 class="feature-title">生成二维码</h2>
        <p class="feature-description">生成包含设备ID的二维码，用于用户信息填写和健康检测</p>
      </a>

      <a href="form/index.html" class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-clipboard-list"></i>
        </div>
        <h2 class="feature-title">信息填写</h2>
        <p class="feature-description">填写个人信息，开始健康检测流程，为您提供个性化的健康分析</p>
      </a>

      <a href="report/index.html" class="feature-card">
        <div class="feature-icon">
          <i class="fas fa-chart-bar"></i>
        </div>
        <h2 class="feature-title">查询报告</h2>
        <p class="feature-description">查询和查看您的健康检测报告，了解您的健康状况和改进建议</p>
      </a>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="footer">
    <div class="footer-content">
      <p class="footer-text">© 2025 健康评估系统由上海康德怡科技版权提供，并结合deepseek大模型AI驱动的智能报告解读系统，提供个性化的健康解决方案，提升生活质量</p>
      <p class="footer-text">提供全面的健康检测解决方案</p>
      <p><a href="admin/index.html" class="admin-link">数据库管理</a></p>
    </div>
  </footer>

  <!-- 引入数据同步服务 -->
  <script src="utils/sync-service.js"></script>
</body>
</html>
