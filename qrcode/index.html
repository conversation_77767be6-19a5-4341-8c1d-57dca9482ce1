<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>二维码生成器</title>
  <script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js"></script>
  <style>
    :root {
      --primary-color: #007bff;
      --primary-hover: #0056b3;
      --success-color: #28a745;
      --danger-color: #dc3545;
      --warning-color: #ffc107;
      --info-color: #17a2b8;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    
    .container {
      background-color: white;
      padding: 40px;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      width: 100%;
      max-width: 450px;
      text-align: center;
    }
    
    h1 {
      color: #333;
      margin-bottom: 30px;
      font-size: 28px;
      font-weight: 600;
    }
    
    .form-group {
      margin-bottom: 20px;
      text-align: left;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 8px;
      color: #555;
      font-weight: 600;
      font-size: 14px;
    }
    
    .form-group input {
      width: calc(100% - 24px);
      padding: 12px;
      border: 2px solid #e1e5e9;
      border-radius: 6px;
      font-size: 16px;
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }
    
    .form-group input:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }
    
    button {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
      color: white;
      padding: 14px 24px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      transition: all 0.3s ease;
      width: 100%;
      margin-top: 20px;
      position: relative;
      overflow: hidden;
    }
    
    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
    }
    
    button:active {
      transform: translateY(0);
    }
    
    #qrcode-container {
      margin-top: 30px;
      padding: 20px;
      background: #f8f9fa;
      border: 2px dashed #dee2e6;
      border-radius: 8px;
      min-height: 100px;
    }
    
    #qrcode-container.hidden {
      display: none;
    }
    
    #qrcode img {
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    #qrcode-url {
      margin-top: 15px;
      padding: 10px;
      background: #e9ecef;
      border-radius: 4px;
      font-size: 12px;
      word-break: break-all;
    }
    
    #qrcode-url a {
      color: var(--primary-color);
      text-decoration: none;
    }
    
    #qrcode-url a:hover {
      text-decoration: underline;
    }
    
    .error-message {
      color: var(--danger-color);
      margin-top: 15px;
      padding: 10px;
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1><i class="fas fa-qrcode"></i> 二维码生成器</h1>

    <div class="form-group">
      <label for="device_id"><i class="fas fa-microchip"></i> 设备ID:</label>
      <input type="text" id="device_id" name="device_id" placeholder="请输入设备ID" value="12345678">
    </div>

    <div class="form-group">
      <label for="owner_name"><i class="fas fa-user"></i> 机主姓名:</label>
      <input type="text" id="owner_name" name="owner_name" placeholder="请输入机主姓名">
    </div>

    <div class="form-group">
      <label for="phone"><i class="fas fa-phone"></i> 机主手机号:</label>
      <input type="tel" id="phone" name="phone" placeholder="请输入11位手机号">
    </div>

    <div class="form-group">
      <label for="session_id"><i class="fas fa-key"></i> 会话ID:</label>
      <input type="text" id="session_id" name="session_id" placeholder="请输入会话ID（可选）">
    </div>

    <button onclick="generateQRCode()">
      <i class="fas fa-qrcode"></i> 生成二维码
    </button>
    
    <div id="error-message" class="error-message" style="display: none;"></div>

    <div id="qrcode-container" style="display: none;">
      <div id="qrcode"></div>
    </div>
  </div>

  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  
  <script>
    function generateQRCode() {
      const deviceIdInput = document.getElementById('device_id');
      const ownerNameInput = document.getElementById('owner_name');
      const phoneInput = document.getElementById('phone');
      const sessionIdInput = document.getElementById('session_id');
      const errorMessageDiv = document.getElementById('error-message');

      // Clear previous error message
      errorMessageDiv.textContent = '';
      errorMessageDiv.style.display = 'none';

      let deviceId = deviceIdInput.value.trim();
      const ownerName = ownerNameInput.value.trim();
      const phone = phoneInput.value.trim();
      const sessionId = sessionIdInput.value.trim();

      // Basic validation
      if (!deviceId) {
        deviceId = '0'; // Default to 0 if empty
      }
      
      // 验证机主姓名
      if (!ownerName) {
        errorMessageDiv.textContent = '请输入机主姓名。';
        errorMessageDiv.style.display = 'block';
        ownerNameInput.focus();
        return;
      }
      
      // 验证机主手机号
      if (!phone) {
        errorMessageDiv.textContent = '请输入机主手机号。';
        errorMessageDiv.style.display = 'block';
        phoneInput.focus();
        return;
      }
      
      if (!/^\d{11}$/.test(phone)) {
        errorMessageDiv.textContent = '请输入正确的11位手机号码。';
        errorMessageDiv.style.display = 'block';
        phoneInput.focus();
        return;
      }
      
      try {
        // Construct the URL for the server-side QR generation API
        const apiUrl = new URL('/api/generate-qr-image', window.location.origin);
        apiUrl.searchParams.append('device_id', deviceId);
        apiUrl.searchParams.append('phone', phone);
        apiUrl.searchParams.append('name', ownerName); // 添加机主姓名参数
        
        if (sessionId) {
          apiUrl.searchParams.append('session_id', sessionId);
        }
        
        console.log('客户端将导航至此URL获取二维码图片:', apiUrl.toString());

        // Redirect the browser to the API endpoint to display/download the image
        window.location.href = apiUrl.toString();

      } catch (error) {
        console.error('构建API URL时出错:', error);
        errorMessageDiv.textContent = '生成二维码链接时出错: ' + error.message;
        errorMessageDiv.style.display = 'block';
      }
    }

    // 为所有输入字段添加回车键监听
    document.addEventListener('DOMContentLoaded', function() {
      const inputFields = ['device_id', 'owner_name', 'phone', 'session_id'];
      
      inputFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
          field.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
              generateQRCode();
            }
          });
        }
      });
    });
  </script>
</body>
</html>
