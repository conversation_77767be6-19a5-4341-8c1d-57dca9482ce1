/**
 * 生成二维码
 * 增强版本：添加了动画效果、错误处理和更好的用户体验
 * 支持机主姓名和机主手机号输入
 */
function generateQRCode() {
  const deviceId = document.getElementById('device_id').value.trim();
  const ownerName = document.getElementById('owner_name').value.trim();
  const ownerPhone = document.getElementById('owner_phone').value.trim();
  const errorDiv = document.getElementById('error-msg');
  const qrcodeDiv = document.getElementById('qrcode');
  const qrcodeUrlDiv = document.getElementById('qrcode-url');
  const qrcodeContainer = document.getElementById('qrcode-container');

  // 清空之前的内容和错误信息
  qrcodeDiv.innerHTML = '';
  qrcodeUrlDiv.textContent = '';
  errorDiv.innerText = "";

  // 验证设备ID
  if (!deviceId) {
    showFieldError('device_id', '请输入设备ID');
    return;
  }

  // 验证机主姓名
  if (!ownerName) {
    showFieldError('owner_name', '请输入机主姓名');
    return;
  }

  // 验证机主手机号
  if (!ownerPhone) {
    showFieldError('owner_phone', '请输入机主手机号');
    return;
  }
  if (!/^\d{11}$/.test(ownerPhone)) {
    showFieldError('owner_phone', '请输入正确的11位手机号码');
    return;
  }

  // 显示加载动画
  qrcodeDiv.innerHTML = '<div class="loading-spinner"></div>';
  qrcodeContainer.classList.remove('hidden');

  try {
    // 获取当前域名和路径
    const currentUrl = window.location.href;
    const baseUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/qrcode'));

    // 构建表单URL，包含所有参数
    const params = new URLSearchParams();
    params.append('device_id', deviceId);
    params.append('phone', ownerPhone);
    params.append('name', ownerName);
    
    const formUrl = `${baseUrl}/form/index.html?${params.toString()}`;

    // 模拟网络延迟（实际应用中可以删除）
    setTimeout(() => {
      try {
        // 生成二维码
        const qr = qrcode(0, 'M');
        qr.addData(formUrl);
        qr.make();

        // 显示二维码
        qrcodeDiv.innerHTML = qr.createImgTag(5);
        const qrImg = qrcodeDiv.querySelector('img');
        if (qrImg) {
          qrImg.style.display = 'block';
          qrImg.style.margin = '0 auto';
          qrImg.alt = '健康检测系统二维码';

          // 添加淡入动画
          qrImg.style.opacity = '0';
          qrImg.style.transition = 'opacity 0.5s ease';
          setTimeout(() => qrImg.style.opacity = '1', 50);
        }

        // 显示URL
        qrcodeUrlDiv.innerHTML = `<i class="fas fa-link"></i> <a href="${formUrl}" target="_blank">${formUrl}</a>`;

        // 显示成功消息
        showToast('二维码生成成功！', 'success');
      } catch (error) {
        console.error('生成二维码失败:', error);
        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 生成二维码失败: ${error.message}`;
        qrcodeContainer.classList.add('hidden');
      }
    }, 500);
  } catch (error) {
    console.error('生成二维码失败:', error);
    errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 生成二维码失败: ${error.message}`;
    qrcodeContainer.classList.add('hidden');
  }
}

/**
 * 显示字段错误信息并添加抖动动画
 * @param {string} fieldId - 字段ID
 * @param {string} message - 错误信息
 */
function showFieldError(fieldId, message) {
  const errorDiv = document.getElementById('error-msg');
  errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
  
  // 添加抖动动画
  const input = document.getElementById(fieldId);
  if (input) {
    input.classList.add('shake');
    input.focus();
    setTimeout(() => input.classList.remove('shake'), 500);
  }
}

/**
 * 显示一个临时的提示消息
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型（success, error, warning, info）
 */
function showToast(message, type = 'info') {
  // 检查是否已存在toast元素，如果存在则移除
  const existingToast = document.querySelector('.toast');
  if (existingToast) {
    document.body.removeChild(existingToast);
  }

  // 创建toast元素
  const toast = document.createElement('div');
  toast.className = `toast toast-${type}`;

  // 设置图标
  let icon = 'info-circle';
  if (type === 'success') icon = 'check-circle';
  if (type === 'error') icon = 'exclamation-circle';
  if (type === 'warning') icon = 'exclamation-triangle';

  toast.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;

  // 添加样式
  toast.style.position = 'fixed';
  toast.style.bottom = '20px';
  toast.style.right = '20px';
  toast.style.backgroundColor = type === 'success' ? '#4caf50' :
                               type === 'error' ? '#f44336' :
                               type === 'warning' ? '#ff9800' : '#2196f3';
  toast.style.color = 'white';
  toast.style.padding = '12px 20px';
  toast.style.borderRadius = '4px';
  toast.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
  toast.style.zIndex = '1000';
  toast.style.opacity = '0';
  toast.style.transition = 'opacity 0.3s ease';

  // 添加到页面
  document.body.appendChild(toast);

  // 显示toast
  setTimeout(() => toast.style.opacity = '1', 10);

  // 3秒后隐藏
  setTimeout(() => {
    toast.style.opacity = '0';
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast);
      }
    }, 300);
  }, 3005);
}

// 为所有输入字段添加回车键监听
document.addEventListener('DOMContentLoaded', function() {
  const inputFields = ['device_id', 'owner_name', 'owner_phone'];
  
  inputFields.forEach(fieldId => {
    const field = document.getElementById(fieldId);
    if (field) {
      field.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          generateQRCode();
        }
      });
    }
  });
});

// 添加抖动动画样式
const style = document.createElement('style');
style.textContent = `
  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
  }

  .shake {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
  }

  .loading-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--primary-color);
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;
document.head.appendChild(style);
