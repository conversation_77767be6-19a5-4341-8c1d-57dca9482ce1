<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>健康评估系统 - 数据库管理</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
  <style>
    body {
      padding: 20px;
      background-color: #f8f9fa;
    }
    .header {
      margin-bottom: 30px;
      padding-bottom: 10px;
      border-bottom: 1px solid #dee2e6;
    }
    .card {
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .table-responsive {
      max-height: 500px;
      overflow-y: auto;
    }
    .action-buttons {
      min-width: 100px;
    }
    .preview-json {
      max-height: 400px;
      overflow-y: auto;
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      font-family: monospace;
    }
    .tab-content {
      padding: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>健康评估系统 - 数据库管理</h1>
      <p class="text-muted">查看和管理用户数据和报告数据</p>
    </div>

    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>数据库信息</h5>
          </div>
          <div class="card-body">
            <ul class="list-group" id="db-info">
              <li class="list-group-item d-flex justify-content-between align-items-center">
                数据库类型
                <span class="badge bg-primary">SQLite</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                数据库路径
                <span class="badge bg-secondary">data/health_check.db</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                用户总数
                <span class="badge bg-info" id="user-count">加载中...</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                报告总数
                <span class="badge bg-info" id="report-count">加载中...</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>操作</h5>
          </div>
          <div class="card-body">
            <div class="d-grid gap-2">
              <button class="btn btn-primary" id="refresh-btn">刷新数据</button>
              <button class="btn btn-success" id="backup-btn">备份数据库</button>
              <button class="btn btn-danger" id="sync-btn">同步文件系统和数据库</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ul class="nav nav-tabs" id="myTab" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab" aria-controls="users" aria-selected="true">用户管理</button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports" type="button" role="tab" aria-controls="reports" aria-selected="false">报告管理</button>
      </li>
    </ul>
    <div class="tab-content" id="myTabContent">
      <div class="tab-pane fade show active" id="users" role="tabpanel" aria-labelledby="users-tab">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5>用户列表</h5>
            <div class="input-group" style="max-width: 300px;">
              <input type="text" class="form-control" id="user-search" placeholder="搜索用户...">
              <button class="btn btn-outline-secondary" type="button" id="search-user-btn">搜索</button>
            </div>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>姓名</th>
                    <th>电话</th>
                    <th>Device ID</th>
                    <th>Session ID</th>
                    <th>创建时间</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="users-table-body">
                  <tr>
                    <td colspan="7" class="text-center">加载中...</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="tab-pane fade" id="reports" role="tabpanel" aria-labelledby="reports-tab">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5>报告列表</h5>
            <div class="input-group" style="max-width: 300px;">
              <input type="text" class="form-control" id="report-search" placeholder="搜索报告...">
              <button class="btn btn-outline-secondary" type="button" id="search-report-btn">搜索</button>
            </div>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>用户</th>
                    <th>报告日期</th>
                    <th>创建时间</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="reports-table-body">
                  <tr>
                    <td colspan="5" class="text-center">加载中...</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 用户详情模态框 -->
  <div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="userModalLabel">用户详情</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="preview-json" id="user-json-preview">
            加载中...
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
          <button type="button" class="btn btn-primary" id="save-user-btn">保存修改</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 报告详情模态框 -->
  <div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="reportModalLabel">报告详情</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="preview-json" id="report-json-preview">
            加载中...
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
          <button type="button" class="btn btn-primary" id="save-report-btn">保存修改</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      // 加载数据库统计信息
      loadDbStats();
      
      // 加载用户列表
      loadUsers();
      
      // 加载报告列表
      loadReports();
      
      // 刷新按钮点击事件
      document.getElementById('refresh-btn').addEventListener('click', function() {
        loadDbStats();
        loadUsers();
        loadReports();
      });
      
      // 备份按钮点击事件
      document.getElementById('backup-btn').addEventListener('click', function() {
        backupDatabase();
      });
      
      // 同步按钮点击事件
      document.getElementById('sync-btn').addEventListener('click', function() {
        syncFileSystemAndDatabase();
      });
      
      // 搜索用户按钮点击事件
      document.getElementById('search-user-btn').addEventListener('click', function() {
        const searchTerm = document.getElementById('user-search').value;
        searchUsers(searchTerm);
      });
      document.getElementById('user-search').addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
          const searchTerm = document.getElementById('user-search').value;
          searchUsers(searchTerm);
        }
      });
      
      // 搜索报告按钮点击事件
      document.getElementById('search-report-btn').addEventListener('click', function() {
        const searchTerm = document.getElementById('report-search').value;
        searchReports(searchTerm);
      });
      document.getElementById('report-search').addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
          const searchTerm = document.getElementById('report-search').value;
          searchReports(searchTerm);
        }
      });
    });
    
    // 加载数据库统计信息
    function loadDbStats() {
      fetch('/api/admin/stats')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            document.getElementById('user-count').textContent = data.userCount;
            document.getElementById('report-count').textContent = data.reportCount;
          } else {
            console.error('Failed to load DB stats:', data.message);
          }
        })
        .catch(error => console.error('Error loading DB stats:', error));
    }
    
    // 加载用户列表
    function loadUsers() {
      fetch('/api/admin/users')
        .then(response => response.json())
        .then(data => {
          const usersTableBody = document.getElementById('users-table-body');
          usersTableBody.innerHTML = ''; // Clear existing rows
          if (data.success && data.users && data.users.length > 0) {
            data.users.forEach(user => {
              const row = usersTableBody.insertRow();
              row.insertCell().textContent = user.id;
              row.insertCell().textContent = user.name;
              row.insertCell().textContent = user.phone;
              row.insertCell().textContent = user.device_id || '0';
              row.insertCell().textContent = user.session_id || '0';
              row.insertCell().textContent = user.created_at ? new Date(user.created_at).toLocaleString() : 'N/A';
              
              const actionsCell = row.insertCell();
              actionsCell.className = 'action-buttons';
              actionsCell.innerHTML = `
                <button class="btn btn-sm btn-info me-1" onclick="viewUserModalTrigger('${user.id}')">查看</button>
                <button class="btn btn-sm btn-danger" onclick="deleteUserModalTrigger('${user.id}')">删除</button>
              `;
            });
          } else if (data.success && data.users && data.users.length === 0) {
            usersTableBody.innerHTML = '<tr><td colspan="7" class="text-center">暂无用户数据</td></tr>';
          } else {
            usersTableBody.innerHTML = '<tr><td colspan="7" class="text-center">加载用户失败: ' + (data.message || '未知错误') + '</td></tr>';
          }
        })
        .catch(error => {
          console.error('Error loading users:', error);
          const usersTableBody = document.getElementById('users-table-body');
          usersTableBody.innerHTML = '<tr><td colspan="7" class="text-center">加载用户出错</td></tr>';
        });
    }
    
    // 加载报告列表
    function loadReports() {
      fetch('/api/admin/reports')
        .then(response => response.json())
        .then(data => {
          const reportsTableBody = document.getElementById('reports-table-body');
          reportsTableBody.innerHTML = ''; // Clear existing rows
          if (data.success && data.reports && data.reports.length > 0) {
            data.reports.forEach(report => {
              const row = reportsTableBody.insertRow();
              row.insertCell().textContent = report.id;
              row.insertCell().textContent = report.user_name || `用户ID: ${report.user_id}`;
              row.insertCell().textContent = report.report_date ? new Date(report.report_date).toLocaleDateString() : 'N/A';
              row.insertCell().textContent = report.created_at ? new Date(report.created_at).toLocaleString() : 'N/A';
              
              const actionsCell = row.insertCell();
              actionsCell.className = 'action-buttons';
              actionsCell.innerHTML = `
                <button class="btn btn-sm btn-info me-1" onclick="viewReportModalTrigger('${report.id}')">查看</button>
                <button class="btn btn-sm btn-danger" onclick="deleteReportModalTrigger('${report.id}')">删除</button>
              `;
            });
          } else if (data.success && data.reports && data.reports.length === 0) {
            reportsTableBody.innerHTML = '<tr><td colspan="5" class="text-center">暂无报告数据</td></tr>';
          } else {
            reportsTableBody.innerHTML = '<tr><td colspan="5" class="text-center">加载报告失败: ' + (data.message || '未知错误') + '</td></tr>';
          }
        })
        .catch(error => {
          console.error('Error loading reports:', error);
          const reportsTableBody = document.getElementById('reports-table-body');
          reportsTableBody.innerHTML = '<tr><td colspan="5" class="text-center">加载报告出错</td></tr>';
        });
    }
    
    // 搜索用户
    function searchUsers(searchTerm) {
      const term = searchTerm.toLowerCase();
      fetch('/api/admin/users') // Fetch all users then filter client-side
        .then(response => response.json())
        .then(data => {
          const usersTableBody = document.getElementById('users-table-body');
          usersTableBody.innerHTML = ''; // Clear existing rows
          if (data.success && data.users) {
            const filteredUsers = data.users.filter(user => 
              (user.name && user.name.toLowerCase().includes(term)) || 
              (user.phone && user.phone.includes(term)) ||
              (user.device_id && String(user.device_id).toLowerCase().includes(term)) ||
              (user.session_id && String(user.session_id).toLowerCase().includes(term))
            );

            if (filteredUsers.length > 0) {
              filteredUsers.forEach(user => {
                const row = usersTableBody.insertRow();
                row.insertCell().textContent = user.id;
                row.insertCell().textContent = user.name;
                row.insertCell().textContent = user.phone;
                row.insertCell().textContent = user.device_id || '0';
                row.insertCell().textContent = user.session_id || '0';
                row.insertCell().textContent = user.created_at ? new Date(user.created_at).toLocaleString() : 'N/A';
                
                const actionsCell = row.insertCell();
                actionsCell.className = 'action-buttons';
                actionsCell.innerHTML = `
                  <button class="btn btn-sm btn-info me-1" onclick="viewUserModalTrigger('${user.id}')">查看</button>
                  <button class="btn btn-sm btn-danger" onclick="deleteUserModalTrigger('${user.id}')">删除</button>
                `;
              });
            } else {
              usersTableBody.innerHTML = '<tr><td colspan="7" class="text-center">未找到匹配用户</td></tr>';
            }
          } else {
            usersTableBody.innerHTML = '<tr><td colspan="7" class="text-center">加载用户失败: ' + (data.message || '未知错误') + '</td></tr>';
          }
        })
        .catch(error => {
          console.error('Error searching users:', error);
          const usersTableBody = document.getElementById('users-table-body');
          usersTableBody.innerHTML = '<tr><td colspan="7" class="text-center">搜索用户出错</td></tr>';
        });
    }
    
    // 显示用户详情
    let userModal = null;
    function viewUserModalTrigger(userId) {
      fetch(`/api/admin/users/${userId}`)
        .then(response => response.json())
        .then(data => {
          if (data.success && data.user) {
            document.getElementById('user-json-preview').textContent = JSON.stringify(data.user, null, 2);
            if (!userModal) {
              userModal = new bootstrap.Modal(document.getElementById('userModal'));
            }
            document.getElementById('save-user-btn').dataset.userId = userId; // Store user ID for saving
            userModal.show();
          } else {
            alert('Failed to load user details: ' + (data.message || 'Unknown error'));
          }
        })
        .catch(error => {
          console.error('Error fetching user details:', error);
          alert('Error fetching user details.');
        });
    }
    
    // 保存用户
    document.getElementById('save-user-btn').addEventListener('click', function() {
      const userId = this.dataset.userId;
      let userData;
      try {
        userData = JSON.parse(document.getElementById('user-json-preview').textContent);
      } catch (e) {
        alert('无法解析用户数据，请确保JSON格式正确。');
        return;
      }

      // 在实际应用中，这里可能需要更复杂的更新逻辑
      // 为了简化，我们假设数据库服务能够处理部分更新或完整替换
      // 但直接通过 /api/users POST (创建/全量更新) 可能更符合现有逻辑
      // 或者需要一个新的 /api/admin/users/:id PUT 端点
      // 这里暂时不实现保存，只关闭模态框
      console.warn('Save user functionality from admin modal is not fully implemented yet. User ID:', userId, 'Data:', userData);
      alert('用户保存功能（管理员）尚未完全实现。');
      if (userModal) {
        userModal.hide();
      }
      // loadUsers(); // Refresh the list after attempting save
    });
    
    // 删除用户
    function deleteUserModalTrigger(userId) {
      if (confirm(`您确定要删除用户 ID ${userId} 吗？此操作也会删除相关报告，且不可恢复。`)) {
        fetch(`/api/admin/users/${userId}`, { method: 'DELETE' })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              alert(data.message || '用户删除成功');
              loadUsers(); // Refresh the list
              loadDbStats(); // Refresh stats
            } else {
              alert('删除用户失败: ' + (data.message || '未知错误'));
            }
          })
          .catch(error => {
            console.error('Error deleting user:', error);
            alert('删除用户出错。');
          });
      }
    }
    
    // 搜索报告
    function searchReports(searchTerm) {
      const term = searchTerm.toLowerCase();
      fetch('/api/admin/reports') 
        .then(response => response.json())
        .then(data => {
          const reportsTableBody = document.getElementById('reports-table-body');
          reportsTableBody.innerHTML = '';
          if (data.success && data.reports) {
            const filteredReports = data.reports.filter(report => 
              (String(report.id).includes(term)) ||
              (report.user_name && report.user_name.toLowerCase().includes(term)) ||
              (report.report_date && new Date(report.report_date).toLocaleDateString().includes(term))
            );
            if (filteredReports.length > 0) {
              filteredReports.forEach(report => {
                const row = reportsTableBody.insertRow();
                row.insertCell().textContent = report.id;
                row.insertCell().textContent = report.user_name || `用户ID: ${report.user_id}`;
                row.insertCell().textContent = report.report_date ? new Date(report.report_date).toLocaleDateString() : 'N/A';
                row.insertCell().textContent = report.created_at ? new Date(report.created_at).toLocaleString() : 'N/A';
                const actionsCell = row.insertCell();
                actionsCell.className = 'action-buttons';
                actionsCell.innerHTML = `
                  <button class="btn btn-sm btn-info me-1" onclick="viewReportModalTrigger('${report.id}')">查看</button>
                  <button class="btn btn-sm btn-danger" onclick="deleteReportModalTrigger('${report.id}')">删除</button>
                `;
              });
            } else {
              reportsTableBody.innerHTML = '<tr><td colspan="5" class="text-center">未找到匹配报告</td></tr>';
            }
          } else {
            reportsTableBody.innerHTML = '<tr><td colspan="5" class="text-center">加载报告失败: ' + (data.message || '未知错误') + '</td></tr>';
          }
        })
        .catch(error => {
          console.error('Error searching reports:', error);
          const reportsTableBody = document.getElementById('reports-table-body');
          reportsTableBody.innerHTML = '<tr><td colspan="5" class="text-center">搜索报告出错</td></tr>';
        });
    }
    
    // 显示报告详情
    let reportModal = null;
    function viewReportModalTrigger(reportId) {
      fetch(`/api/admin/reports/${reportId}`)
        .then(response => response.json())
        .then(data => {
          if (data.success && data.report) {
            document.getElementById('report-json-preview').textContent = JSON.stringify(data.report, null, 2);
            if (!reportModal) {
              reportModal = new bootstrap.Modal(document.getElementById('reportModal'));
            }
            document.getElementById('save-report-btn').dataset.reportId = reportId; // Store report ID for saving
            reportModal.show();
          } else {
            alert('Failed to load report details: ' + (data.message || 'Unknown error'));
          }
        })
        .catch(error => {
          console.error('Error fetching report details:', error);
          alert('Error fetching report details.');
        });
    }
    
    // 保存报告
    document.getElementById('save-report-btn').addEventListener('click', function() {
      const reportId = this.dataset.reportId;
      let reportData;
      try {
        reportData = JSON.parse(document.getElementById('report-json-preview').textContent);
      } catch (e) {
        alert('无法解析报告数据，请确保JSON格式正确。');
        return;
      }
      console.warn('Save report functionality from admin modal is not fully implemented yet. Report ID:', reportId, 'Data:', reportData);
      alert('报告保存功能（管理员）尚未完全实现。');
      if (reportModal) {
        reportModal.hide();
      }
      // loadReports(); // Refresh the list
    });
    
    // 删除报告
    function deleteReportModalTrigger(reportId) {
      if (confirm(`您确定要删除报告 ID ${reportId} 吗？此操作不可恢复。`)) {
        fetch(`/api/admin/reports/${reportId}`, { method: 'DELETE' })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              alert(data.message || '报告删除成功');
              loadReports(); // Refresh the list
              loadDbStats(); // Refresh stats
            } else {
              alert('删除报告失败: ' + (data.message || '未知错误'));
            }
          })
          .catch(error => {
            console.error('Error deleting report:', error);
            alert('删除报告出错。');
          });
      }
    }
    
    // 备份数据库
    function backupDatabase() {
      if (!confirm('您确定要备份当前数据库吗？')) return;
      fetch('/api/admin/backup', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            alert('数据库备份成功！路径: ' + data.backupPath);
          } else {
            alert('数据库备份失败: ' + (data.message || '未知错误'));
          }
        })
        .catch(error => {
          console.error('Error backing up database:', error);
          alert('数据库备份出错。');
        });
    }
    
    // 同步文件系统和数据库
    function syncFileSystemAndDatabase() {
      if (!confirm('您确定要同步文件系统和数据库吗？这可能需要一些时间。')) return;
      fetch('/api/admin/sync', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            alert(`同步完成！\n成功同步用户数: ${data.usersSynced}\n成功同步报告数: ${data.reportsSynced}\n失败数: ${data.errors}`);
            loadDbStats();
            loadUsers();
            loadReports();
          } else {
            alert('同步失败: ' + (data.message || '未知错误'));
          }
        })
        .catch(error => {
          console.error('Error syncing:', error);
          alert('同步出错。');
        });
    }
  </script>
</body>
</html> 