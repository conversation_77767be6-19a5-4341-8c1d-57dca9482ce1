@echo off
echo =====================================
echo     Health Check System Launcher
echo =====================================
echo.

rem Check if node_modules directory exists
if not exist "node_modules" (
  echo Node modules not found. Installing dependencies...
  echo.
  call npm install
  if %ERRORLEVEL% neq 0 (
    echo Error installing dependencies. Please check your npm installation.
    pause
    exit /b 1
  )
  echo Dependencies installed successfully.
  echo.
)

rem Verify database
if not exist "data" (
  echo Creating data directory...
  mkdir "data"
)

echo Initializing database...
node -e "require('./utils/database-service').initDatabaseService().then(() => console.log('Database initialization complete!')).catch(err => {console.error('Error initializing database:', err); process.exit(1);})"
if %ERRORLEVEL% neq 0 (
  echo Database initialization failed. Please check the error message.
  pause
  exit /b 1
)
echo.

echo Loading default user data (<PERSON>)...
node utils/init-default-data.js
if %ERRORLEVEL% neq 0 (
  echo Warning: Default user data loading failed. System will continue without sample data.
) else (
  echo Default user data loaded successfully.
  echo NOTE: Health check reports are not auto-generated. They can only be synced from external API.
)
echo.

echo Starting server...
start "Health Check Server" cmd /k node server.js

echo Waiting for server to start...
timeout /t 3 > nul

echo Opening application in browser...
start "" http://localhost:3005

echo.
echo =====================================
echo Server is running at http://localhost:3005
echo Keep this window open while using the system.
echo Press Ctrl+C in the server window to stop the server.
echo.
echo IMPORTANT: Health check reports can ONLY be synced
echo            from external API. No manual or automatic
echo            generation is allowed.
echo =====================================
echo.
